<?php

$disk = isset($_GET['disk']) ? (string) $_GET['disk'] : '';
$file = isset($_GET['file']) ? (string) $_GET['file'] : '';

if ($disk === '' || $file === '') {
    http_response_code(404);
    exit;
}

if (preg_match('/^[a-z0-9_-]+$/i', $disk) !== 1) {
    http_response_code(404);
    exit;
}

if (preg_match('/^[a-z0-9]+(?:-[a-z0-9]+)*-[0-9]{8}-[0-9]{6}(?:-[0-9]+)?\.(jpg|jpeg|png|webp)$/i', $file) !== 1) {
    http_response_code(404);
    exit;
}

$projectRoot = __DIR__;
$directories = [
    'assets' => $projectRoot . '/assets/uploads/categories',
    'storage' => $projectRoot . '/storage/uploads/categories',
    'temp' => rtrim(sys_get_temp_dir(), DIRECTORY_SEPARATOR) . '/rcf-category-images',
];

if (!array_key_exists($disk, $directories)) {
    http_response_code(404);
    exit;
}

$absolutePath = rtrim($directories[$disk], "/\\") . '/' . $file;

if (!is_file($absolutePath) || !is_readable($absolutePath)) {
    http_response_code(404);
    exit;
}

$mimeType = 'application/octet-stream';
if (class_exists('finfo')) {
    $finfo = new finfo(FILEINFO_MIME_TYPE);
    $detected = $finfo->file($absolutePath);
    if (is_string($detected) && $detected !== '') {
        $mimeType = $detected;
    }
}

header('Content-Type: ' . $mimeType);
$size = filesize($absolutePath);
if (is_int($size) || is_float($size)) {
    header('Content-Length: ' . (int) $size);
}
header('Cache-Control: public, max-age=31536000, immutable');

$handle = fopen($absolutePath, 'rb');
if ($handle === false) {
    http_response_code(500);
    exit;
}

while (!feof($handle)) {
    $chunk = fread($handle, 8192);
    if ($chunk === false) {
        fclose($handle);
        http_response_code(500);
        exit;
    }
    echo $chunk;
}

fclose($handle);
exit;
