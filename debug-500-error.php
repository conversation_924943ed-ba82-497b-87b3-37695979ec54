<?php
// Debug script for 500 Internal Server Error
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('log_errors', 1);

echo "<!DOCTYPE html><html><head><title>500 Error Debug</title></head><body>";
echo "<h1>500 Error Diagnostic</h1>";

echo "<h2>1. PHP Environment Check</h2>";
echo "<p>PHP Version: " . phpversion() . "</p>";
echo "<p>Error Reporting: " . error_reporting() . "</p>";
echo "<p>Display Errors: " . ini_get('display_errors') . "</p>";
echo "<p>Memory Limit: " . ini_get('memory_limit') . "</p>";
echo "<p>Max Execution Time: " . ini_get('max_execution_time') . "</p>";

echo "<h2>2. File Permissions Check</h2>";
$files_to_check = [
    'admin/categories.php',
    'bootstrap.php',
    'components/admin/index.php',
    'components/admin/categories/index.php',
    '.env'
];

foreach ($files_to_check as $file) {
    if (file_exists($file)) {
        $perms = substr(sprintf('%o', fileperms($file)), -4);
        echo "<p>✅ $file - Permissions: $perms</p>";
    } else {
        echo "<p>❌ $file - NOT FOUND</p>";
    }
}

echo "<h2>3. Directory Structure Check</h2>";
$dirs_to_check = [
    'admin',
    'components',
    'components/admin',
    'components/admin/categories',
    'src',
    'storage'
];

foreach ($dirs_to_check as $dir) {
    if (is_dir($dir)) {
        $perms = substr(sprintf('%o', fileperms($dir)), -4);
        echo "<p>✅ $dir/ - Permissions: $perms</p>";
    } else {
        echo "<p>❌ $dir/ - NOT FOUND</p>";
    }
}

echo "<h2>4. Bootstrap Test</h2>";
try {
    require_once 'bootstrap.php';
    echo "<p>✅ Bootstrap loaded successfully</p>";
    
    // Test MongoDB connection
    if (function_exists('is_mongodb_available')) {
        $mongoAvailable = is_mongodb_available();
        echo "<p>MongoDB Available: " . ($mongoAvailable ? '✅ YES' : '❌ NO') . "</p>";
    }
    
} catch (Exception $e) {
    echo "<p>❌ Bootstrap Error: " . htmlspecialchars($e->getMessage()) . "</p>";
} catch (Error $e) {
    echo "<p>❌ Bootstrap Fatal Error: " . htmlspecialchars($e->getMessage()) . "</p>";
}

echo "<h2>5. Admin Components Test</h2>";
try {
    require_once 'components/admin/index.php';
    echo "<p>✅ Admin components loaded successfully</p>";
} catch (Exception $e) {
    echo "<p>❌ Admin Components Error: " . htmlspecialchars($e->getMessage()) . "</p>";
} catch (Error $e) {
    echo "<p>❌ Admin Components Fatal Error: " . htmlspecialchars($e->getMessage()) . "</p>";
}

echo "<h2>6. Categories Components Test</h2>";
try {
    require_once 'components/admin/categories/index.php';
    echo "<p>✅ Categories components loaded successfully</p>";
    
    // Test CategoryRepository
    if (class_exists('CategoryRepository')) {
        $repository = new CategoryRepository();
        echo "<p>✅ CategoryRepository created successfully</p>";
        
        $service = new CategoryService($repository);
        echo "<p>✅ CategoryService created successfully</p>";
    }
    
} catch (Exception $e) {
    echo "<p>❌ Categories Components Error: " . htmlspecialchars($e->getMessage()) . "</p>";
} catch (Error $e) {
    echo "<p>❌ Categories Components Fatal Error: " . htmlspecialchars($e->getMessage()) . "</p>";
}

echo "<h2>7. Categories Page Test</h2>";
try {
    // Set up environment like web server
    $_SERVER['REQUEST_METHOD'] = 'GET';
    $_GET = [];
    
    ob_start();
    include 'admin/categories.php';
    $output = ob_get_contents();
    ob_end_clean();
    
    echo "<p>✅ Categories page loaded successfully</p>";
    echo "<p>Output length: " . strlen($output) . " characters</p>";
    
    // Check for errors in output
    if (strpos($output, 'Fatal error') !== false) {
        echo "<p>❌ Fatal error found in output</p>";
    }
    if (strpos($output, 'Warning') !== false) {
        echo "<p>⚠️ Warning found in output</p>";
    }
    
} catch (Exception $e) {
    echo "<p>❌ Categories Page Error: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p>File: " . $e->getFile() . " Line: " . $e->getLine() . "</p>";
} catch (Error $e) {
    echo "<p>❌ Categories Page Fatal Error: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p>File: " . $e->getFile() . " Line: " . $e->getLine() . "</p>";
}

echo "<h2>8. Web Server Environment</h2>";
echo "<p>Document Root: " . ($_SERVER['DOCUMENT_ROOT'] ?? 'Not set') . "</p>";
echo "<p>Script Name: " . ($_SERVER['SCRIPT_NAME'] ?? 'Not set') . "</p>";
echo "<p>Request URI: " . ($_SERVER['REQUEST_URI'] ?? 'Not set') . "</p>";
echo "<p>HTTP Host: " . ($_SERVER['HTTP_HOST'] ?? 'Not set') . "</p>";
echo "<p>Server Software: " . ($_SERVER['SERVER_SOFTWARE'] ?? 'Not set') . "</p>";

echo "<h2>9. Error Log Check</h2>";
$error_log_locations = [
    ini_get('error_log'),
    '/var/log/apache2/error.log',
    '/var/log/nginx/error.log',
    'storage/logs/php_errors.log',
    'admin/storage/logs/php_errors.log'
];

foreach ($error_log_locations as $log_file) {
    if ($log_file && file_exists($log_file) && is_readable($log_file)) {
        echo "<p>📋 Error log found: $log_file</p>";
        $recent_errors = tail($log_file, 10);
        if ($recent_errors) {
            echo "<pre style='background:#f5f5f5;padding:10px;'>" . htmlspecialchars($recent_errors) . "</pre>";
        }
        break;
    }
}

echo "<h2>10. Quick Fix Test</h2>";
echo "<p><a href='admin/categories.php' target='_blank'>🔗 Test Categories Page Direct Link</a></p>";
echo "<p><a href='mongodb-demo.php' target='_blank'>🔗 Test MongoDB Demo Page</a></p>";

function tail($filename, $lines = 10) {
    $handle = fopen($filename, "r");
    if (!$handle) return false;
    
    $linecounter = $lines;
    $pos = -2;
    $beginning = false;
    $text = array();
    
    while ($linecounter > 0) {
        $t = " ";
        while ($t != "\n") {
            if (fseek($handle, $pos, SEEK_END) == -1) {
                $beginning = true;
                break;
            }
            $t = fgetc($handle);
            $pos--;
        }
        $linecounter--;
        if ($beginning) {
            rewind($handle);
        }
        $text[$lines-$linecounter-1] = fgets($handle);
        if ($beginning) break;
    }
    fclose($handle);
    return implode("", array_reverse($text));
}

echo "</body></html>";
?>
