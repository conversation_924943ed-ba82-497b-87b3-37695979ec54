<?php
// Robust path resolution for web server environment
$rootDir = null;

// Method 1: Use __DIR__ and go up one level
if (file_exists(__DIR__ . '/../bootstrap.php')) {
    $rootDir = __DIR__ . '/..';
}
// Method 2: Use dirname(__DIR__)
elseif (file_exists(dirname(__DIR__) . '/bootstrap.php')) {
    $rootDir = dirname(__DIR__);
}
// Method 3: Use realpath
elseif (file_exists(realpath(__DIR__ . '/../bootstrap.php'))) {
    $rootDir = realpath(__DIR__ . '/..');
}
// Method 4: Check if we're already in the right place
elseif (file_exists('./bootstrap.php')) {
    $rootDir = '.';
}
else {
    // Error handling
    http_response_code(500);
    die('Error: Cannot locate bootstrap.php. Please check file structure.');
}

require_once $rootDir . '/bootstrap.php';
require_once $rootDir . '/components/admin/index.php';
require_once $rootDir . '/components/admin/categories/index.php';

$repository = new CategoryRepository();
$service = new CategoryService($repository);
$statusOptions = $service->getStatusOptions();
$requestMethod = $_SERVER['REQUEST_METHOD'] ?? 'GET';

$performCategoryDeletion = static function (string $deleteId) use ($service) {
    if ($deleteId === '') {
        Flash::add('error', 'Category selection is invalid.');
        header('Location: categories.php');
        exit;
    }

    $result = $service->deleteCategory($deleteId);
    if ($result['success']) {
        Flash::add('success', $result['message'] ?? 'Category deleted successfully.');
    } else {
        $message = $result['message'] ?? ($result['errors']['general'] ?? 'Failed to delete category.');
        Flash::add('error', $message);
    }

    header('Location: categories.php');
    exit;
};

// Handle delete action via POST (primary flow)
if ($requestMethod === 'POST' && ($_POST['action'] ?? '') === 'delete') {
    $deleteId = isset($_POST['id']) ? trim((string) $_POST['id']) : '';
    $performCategoryDeletion($deleteId);
}

// Graceful fallback for legacy GET requests
if (isset($_GET['action']) && $_GET['action'] === 'delete' && isset($_GET['id'])) {
    $deleteId = trim((string) $_GET['id']);
    $performCategoryDeletion($deleteId);
}

$page = isset($_GET['page']) ? max(1, (int) $_GET['page']) : 1;
$perPage = 10;
$search = trim((string) ($_GET['search'] ?? ''));
$statusFilter = $_GET['status'] ?? '';
if ($statusFilter !== '' && !array_key_exists($statusFilter, $statusOptions)) {
    $statusFilter = '';
}

$pagination = $service->paginateCategories($page, $perPage, $search ?: null, $statusFilter ?: null);
$categories = $pagination['data'];
$totalPages = $pagination['total_pages'];
$currentPage = $pagination['current_page'];
$totalCount = $pagination['total'];

$flashMessages = Flash::consume();

$dashboardData = get_admin_dashboard_data();
foreach ($dashboardData['sidebar']['menu'] as &$menuItem) {
    if (($menuItem['href'] ?? '') === 'categories.php') {
        $menuItem['is_active'] = true;
    }
}
unset($menuItem);

if (!function_exists('format_category_date')) {
    function format_category_date($timestamp): string
    {
        if ($timestamp instanceof DateTimeInterface) {
            $date = DateTimeImmutable::createFromInterface($timestamp);
            return $date->setTimezone(new DateTimeZone(date_default_timezone_get()))->format('M d, Y');
        }

        if (is_array($timestamp) && isset($timestamp['date'])) {
            $timestamp = $timestamp['date'];
        }

        if (!is_string($timestamp) || trim($timestamp) === '') {
            return '—';
        }
        try {
            $date = new DateTimeImmutable($timestamp, new DateTimeZone('UTC'));
            return $date->setTimezone(new DateTimeZone(date_default_timezone_get()))->format('M d, Y');
        } catch (Throwable $exception) {
            return $timestamp;
        }
    }
}

if (!function_exists('build_category_query')) {
    function build_category_query(array $overrides = []): string
    {
        $base = [
            'search' => $_GET['search'] ?? null,
            'status' => $_GET['status'] ?? null,
        ];
        $query = array_merge($base, $overrides);

        return http_build_query(array_filter($query, static fn ($value) => $value !== null && $value !== ''));
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="description" content="rcf-Grocery-Store(e-Commerce) HTML Template: A sleek, responsive, and user-friendly HTML template designed for online grocery stores.">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="keywords" content="Grocery, Store, stores">
    <title>RC Furnishing Admin – Categories</title>
    <link rel="shortcut icon" type="image/x-icon" href="assets/images/fav.png">
    <link rel="stylesheet preload" href="assets/css/plugins.css" as="style">
    <link rel="stylesheet preload" href="https://cdn.datatables.net/1.10.15/css/jquery.dataTables.min.css" as="style">
    <link rel="stylesheet preload" href="assets/css/style.css" as="style">
    <style>
        .category-page .category-card {
            padding: 24px;
            background: #ffffff;
            border-radius: 16px;
            box-shadow: 0 12px 30px rgba(35, 46, 60, 0.08);
        }

        .category-page__filters {
            display: flex;
            align-items: center;
            gap: 12px;
            flex-wrap: wrap;
        }

        .category-page__filters input[type="text"] {
            min-width: 200px;
            flex: 1 1 200px;
            padding: 12px 16px;
            border-radius: 12px;
            border: 1px solid #E2E8F0;
        }

        .category-page__filters .nice-select,
        .category-page__filters select {
            min-width: 160px;
        }

        .category-page__alerts {
            margin-bottom: 20px;
        }

        .category-page__alert {
            padding: 12px 16px;
            border-radius: 8px;
            margin-bottom: 12px;
            font-weight: 500;
        }

        .category-page__alert--success {
            color: #1b5e20;
            background: #e8f5e9;
        }

        .category-page__alert--error {
            color: #d32f2f;
            background: #fdecea;
        }

        .category-page__table thead th,
        .category-page__table tbody td {
            white-space: normal;
            vertical-align: middle;
        }

        .category-page__empty td {
            text-align: center;
        }

        .category-page__table .rts-btn.btn-danger {
            color: #d32f2f;
            border-color: #d32f2f;
            margin-left: 8px;
        }

        .category-page__table .rts-btn.btn-danger:hover {
            background-color: #d32f2f;
            color: white;
        }

        @media (max-width: 991px) {
            .category-page__filters {
                flex-direction: column;
                align-items: stretch;
            }

            .category-page__filters input[type="text"],
            .category-page__filters .nice-select,
            .category-page__filters select,
            .category-page__filters button {
                width: 100%;
            }
        }

        @media (max-width: 767px) {
            .category-page__table thead {
                display: none;
            }

            .category-page__table tbody tr {
                display: block;
                border: 1px solid #edf2f7;
                border-radius: 12px;
                padding: 16px;
                margin-bottom: 16px;
            }

            .category-page__table tbody tr td {
                display: flex;
                flex-direction: column;
                gap: 6px;
                padding: 8px 0;
                border: none;
            }

            .category-page__table tbody tr td::before {
                content: attr(data-label);
                font-weight: 600;
                color: #64748b;
            }

            .category-page__table tbody tr td:last-child .rts-btn {
                width: 100%;
                text-align: center;
            }
        }
    </style>
</head>
<body>
    <div class="rcf_dashboard">
        <?php render_admin_sidebar($dashboardData['sidebar']); ?>
        <div class="right-area-body-content">
            <?php render_admin_header($dashboardData['header']); ?>
            <div class="body-root-inner">
                <div class="transection category-page">
                    <div class="row g-5 align-items-start">
                        <div class="col-12">
                            <div class="category-card mb--20">
                                <div class="title-right-actioin-btn-wrapper-product-list">
                                    <h3 class="title">Categories</h3>
                                    <a href="add-category.php" class="rts-btn btn-primary">+ Add Category</a>
                                </div>
                                <form method="get" class="category-page__filters mb--20">
                                    <input type="text" name="search" value="<?= htmlspecialchars($search, ENT_QUOTES, 'UTF-8'); ?>" placeholder="Search categories">
                                    <select name="status" class="mySelect">
                                        <option value="" data-display="All Statuses">All Statuses</option>
                                        <?php foreach ($statusOptions as $value => $label): ?>
                                            <option value="<?= htmlspecialchars($value, ENT_QUOTES, 'UTF-8'); ?>" data-display="<?= htmlspecialchars($label, ENT_QUOTES, 'UTF-8'); ?>"<?= $statusFilter === $value ? ' selected' : ''; ?>><?= htmlspecialchars($label, ENT_QUOTES, 'UTF-8'); ?></option>
                                        <?php endforeach; ?>
                                    </select>
                                    <button type="submit" class="rts-btn btn-primary">Filter</button>
                                </form>

                                <?php if (!empty($flashMessages)): ?>
                                    <div class="category-page__alerts">
                                        <?php foreach ($flashMessages as $type => $messages): ?>
                                            <?php foreach ($messages as $message): ?>
                                                <div class="category-page__alert category-page__alert--<?= $type === 'success' ? 'success' : 'error'; ?>">
                                                    <?= htmlspecialchars($message, ENT_QUOTES, 'UTF-8'); ?>
                                                </div>
                                            <?php endforeach; ?>
                                        <?php endforeach; ?>
                                    </div>
                                <?php endif; ?>

                                <div class="table-responsive">
                                    <table class="table table-hover category-page__table">
                                        <thead>
                                            <tr>
                                                <th style="min-width: 160px;">Name</th>
                                                <th>Description</th>
                                                <th style="min-width: 120px;">Status</th>
                                                <th style="min-width: 140px;">Created</th>
                                                <th style="min-width: 100px;" class="text-end">Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php if (empty($categories)): ?>
                                                <tr class="category-page__empty">
                                                    <td colspan="5">No categories found. Start by creating a new one.</td>
                                                </tr>
                                            <?php else: ?>
                                                <?php foreach ($categories as $category): ?>
                                                    <tr>
                                                        <td data-label="Name">
                                                            <strong><?= htmlspecialchars($category['name'], ENT_QUOTES, 'UTF-8'); ?></strong>
                                                        </td>
                                                        <td data-label="Description"><?= htmlspecialchars($category['description'] ?? '—', ENT_QUOTES, 'UTF-8'); ?></td>
                                                        <td data-label="Status">
                                                            <span style="display:inline-block; padding:4px 10px; border-radius:999px; font-size:12px; text-transform:capitalize; background:<?= $category['status'] === 'active' ? '#e8f5e9' : '#fff4e5'; ?>; color:<?= $category['status'] === 'active' ? '#1b5e20' : '#7a4100'; ?>;">
                                                                <?= htmlspecialchars($category['status'], ENT_QUOTES, 'UTF-8'); ?>
                                                            </span>
                                                        </td>
                                                        <td data-label="Created"><?= htmlspecialchars(format_category_date($category['created_at']), ENT_QUOTES, 'UTF-8'); ?></td>
                                                        <td data-label="Actions" class="text-end">
                                                            <a class="rts-btn btn-primary bg-transparent" href="add-category.php?action=edit&id=<?= rawurlencode($category['id']); ?>">Edit</a>
                                                            <button type="button" class="rts-btn btn-danger bg-transparent category-delete-button" data-category-id="<?= htmlspecialchars($category['id'], ENT_QUOTES, 'UTF-8'); ?>" data-category-name="<?= htmlspecialchars($category['name'], ENT_QUOTES, 'UTF-8'); ?>">Delete</button>
                                                        </td>
                                                    </tr>
                                                <?php endforeach; ?>
                                            <?php endif; ?>
                                        </tbody>
                                    </table>
                                </div>

                                <?php if ($totalPages > 1): ?>
                                    <div class="pagination" style="display:flex; justify-content:space-between; align-items:center; margin-top:20px;">
                                        <div>
                                            Page <?= $currentPage; ?> of <?= $totalPages; ?>
                                        </div>
                                        <div style="display:flex; gap:8px;">
                                            <?php
                                                $prevPage = max(1, $currentPage - 1);
                                                $nextPage = min($totalPages, $currentPage + 1);
                                                $prevQuery = build_category_query(['page' => $prevPage]);
                                                $nextQuery = build_category_query(['page' => $nextPage]);
                                                $prevHref = 'categories.php' . ($prevQuery !== '' ? '?' . $prevQuery : '');
                                                $nextHref = 'categories.php' . ($nextQuery !== '' ? '?' . $nextQuery : '');
                                            ?>
                                            <a class="rts-btn btn-primary bg-transparent" href="<?= htmlspecialchars($prevHref, ENT_QUOTES, 'UTF-8'); ?>"<?= $currentPage === 1 ? ' style="pointer-events:none; opacity:0.5;"' : ''; ?>>Prev</a>
                                            <a class="rts-btn btn-primary bg-transparent" href="<?= htmlspecialchars($nextHref, ENT_QUOTES, 'UTF-8'); ?>"<?= $currentPage === $totalPages ? ' style="pointer-events:none; opacity:0.5;"' : ''; ?>>Next</a>
                                        </div>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
                <form method="post" id="delete-category-form" style="display:none;">
                    <input type="hidden" name="action" value="delete">
                    <input type="hidden" name="id" id="delete-category-id" value="">
                </form>
                <?php render_admin_footer($dashboardData['footer']); ?>
            </div>
        </div>
    </div>
    <?php render_admin_overlays(); ?>
    <script src="assets/js/plugins.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/apexcharts@3.27.0/dist/apexcharts.min.js"></script>
    <script src="https://cdn.datatables.net/1.10.15/js/jquery.dataTables.min.js"></script>
    <script src="assets/js/main.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function () {
            var deleteButtons = document.querySelectorAll('.category-delete-button');
            var deleteForm = document.getElementById('delete-category-form');
            var deleteIdField = document.getElementById('delete-category-id');

            function submitDeletion(categoryId, categoryName) {
                if (!deleteForm || !deleteIdField) {
                    return;
                }

                var message = 'Are you sure you want to delete the category "' + categoryName + '"? This action cannot be undone.';
                if (!window.confirm(message)) {
                    return;
                }

                deleteIdField.value = categoryId;
                deleteForm.submit();
            }

            deleteButtons.forEach(function (button) {
                button.addEventListener('click', function (event) {
                    event.preventDefault();
                    var categoryId = button.getAttribute('data-category-id');
                    var categoryName = button.getAttribute('data-category-name') || 'this category';
                    if (!categoryId) {
                        return;
                    }
                    submitDeletion(categoryId, categoryName);
                });
            });
        });
    </script>
</body>
</html>
