<?php
$rootDir = null;

if (file_exists(__DIR__ . '/../bootstrap.php')) {
    $rootDir = __DIR__ . '/..';
} elseif (file_exists(dirname(__DIR__) . '/bootstrap.php')) {
    $rootDir = dirname(__DIR__);
} elseif (file_exists(realpath(__DIR__ . '/../bootstrap.php'))) {
    $rootDir = realpath(__DIR__ . '/..');
} elseif (file_exists('./bootstrap.php')) {
    $rootDir = '.';
} else {
    http_response_code(500);
    die('Error: Cannot locate bootstrap.php. Please check file structure.');
}

require_once $rootDir . '/bootstrap.php';
require_once $rootDir . '/components/admin/index.php';
require_once $rootDir . '/components/admin/categories/index.php';
require_once $rootDir . '/components/admin/products/index.php';

$productRepository = getProductRepository();
$productService = new ProductService($productRepository);
$statusOptions = $productService->getStatusOptions();

$categoryRepository = new CategoryRepository();
$categoryService = new CategoryService($categoryRepository);
$categoryOptions = $categoryService->listAllCategories();
$categoryLookup = [];
foreach ($categoryOptions as $categoryOption) {
    $categoryLookup[$categoryOption['id']] = $categoryOption['name'];
}

$requestMethod = $_SERVER['REQUEST_METHOD'] ?? 'GET';

$performProductDeletion = static function (string $deleteId) use ($productService) {
    if ($deleteId === '') {
        Flash::add('error', 'Product selection is invalid.');
        header('Location: product-list.php');
        exit;
    }

    $result = $productService->deleteProduct($deleteId);
    if ($result['success']) {
        Flash::add('success', $result['message'] ?? 'Product deleted successfully.');
    } else {
        $message = $result['message'] ?? ($result['errors']['general'] ?? 'Failed to delete product.');
        Flash::add('error', $message);
    }

    header('Location: product-list.php');
    exit;
};

if ($requestMethod === 'POST' && ($_POST['action'] ?? '') === 'delete') {
    $deleteId = isset($_POST['id']) ? trim((string) $_POST['id']) : '';
    $performProductDeletion($deleteId);
}

if (isset($_GET['action']) && $_GET['action'] === 'delete' && isset($_GET['id'])) {
    $deleteId = trim((string) $_GET['id']);
    $performProductDeletion($deleteId);
}

$page = isset($_GET['page']) ? max(1, (int) $_GET['page']) : 1;
$perPage = 10;
$search = trim((string) ($_GET['search'] ?? ''));
$statusFilter = $_GET['status'] ?? '';
if ($statusFilter !== '' && !array_key_exists($statusFilter, $statusOptions)) {
    $statusFilter = '';
}

$categoryFilter = trim((string) ($_GET['category'] ?? ''));
if ($categoryFilter !== '' && !array_key_exists($categoryFilter, $categoryLookup)) {
    $categoryFilter = '';
}

$pagination = $productService->paginateProducts($page, $perPage, $search ?: null, $statusFilter ?: null, $categoryFilter ?: null);
$products = $pagination['data'];
$totalPages = $pagination['total_pages'];
$currentPage = $pagination['current_page'];
$totalCount = $pagination['total'];

$flashMessages = Flash::consume();

$dashboardData = get_admin_dashboard_data();
foreach ($dashboardData['sidebar']['menu'] as &$menuItem) {
    if (($menuItem['href'] ?? '') === 'product-list.php') {
        $menuItem['is_active'] = true;
    }
}
unset($menuItem);

if (!function_exists('format_product_date')) {
    function format_product_date($timestamp): string
    {
        if ($timestamp instanceof DateTimeInterface) {
            $date = DateTimeImmutable::createFromInterface($timestamp);
            return $date->setTimezone(new DateTimeZone(date_default_timezone_get()))->format('M d, Y');
        }

        if (is_array($timestamp) && isset($timestamp['date'])) {
            $timestamp = $timestamp['date'];
        }

        if (!is_string($timestamp) || trim($timestamp) === '') {
            return '—';
        }

        try {
            $date = new DateTimeImmutable($timestamp, new DateTimeZone('UTC'));
            return $date->setTimezone(new DateTimeZone(date_default_timezone_get()))->format('M d, Y');
        } catch (Throwable $exception) {
            return $timestamp;
        }
    }
}

if (!function_exists('format_product_price')) {
    function format_product_price($price): string
    {
        if ($price === null || $price === '') {
            return '—';
        }

        if (!is_numeric($price)) {
            return (string) $price;
        }

        return '$' . number_format((float) $price, 2);
    }
}

if (!function_exists('product_status_label')) {
    function product_status_label(string $status): string
    {
        $labels = [
            'active' => 'Active',
            'inactive' => 'Inactive',
            'draft' => 'Draft',
            'out_of_stock' => 'Out of Stock',
        ];

        return $labels[$status] ?? ucfirst($status);
    }
}

if (!function_exists('product_status_modifier')) {
    function product_status_modifier(string $status): string
    {
        $allowed = ['active', 'inactive', 'draft', 'out_of_stock'];
        return in_array($status, $allowed, true) ? $status : 'inactive';
    }
}

if (!function_exists('build_product_query')) {
    function build_product_query(array $overrides = []): string
    {
        $base = [
            'search' => $_GET['search'] ?? null,
            'status' => $_GET['status'] ?? null,
            'category' => $_GET['category'] ?? null,
        ];
        $query = array_merge($base, $overrides);

        return http_build_query(array_filter($query, static fn ($value) => $value !== null && $value !== ''));
    }
}

$placeholderProductImage = 'assets/images/grocery/16.png';
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="description" content="rcf-Grocery-Store(e-Commerce) HTML Template: A sleek, responsive, and user-friendly HTML template designed for online grocery stores.">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="keywords" content="Grocery, Store, stores">
    <title>RC Furnishing Admin – Products</title>
    <link rel="shortcut icon" type="image/x-icon" href="assets/images/fav.png">
    <link rel="stylesheet preload" href="assets/css/plugins.css" as="style">
    <link rel="stylesheet preload" href="https://cdn.datatables.net/1.10.15/css/jquery.dataTables.min.css" as="style">
    <link rel="stylesheet preload" href="assets/css/style.css" as="style">
    <style>
        .admin-table-card {
            padding: 24px;
            background: #ffffff;
            border-radius: 16px;
            box-shadow: 0 12px 30px rgba(35, 46, 60, 0.08);
        }

        .admin-table-card__header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            gap: 16px;
            flex-wrap: wrap;
        }

        .admin-table-card__heading {
            display: flex;
            flex-direction: column;
            gap: 8px;
            min-width: 0;
        }

        .admin-table-card__title {
            margin: 0;
            font-size: 24px;
            font-weight: 600;
            color: #0f172a;
        }

        .admin-table-card__meta {
            display: flex;
            flex-wrap: wrap;
            gap: 12px;
            color: #475569;
            font-size: 14px;
        }

        .admin-table-card__meta-item {
            display: inline-flex;
            align-items: center;
            gap: 4px;
            line-height: 1.4;
        }

        .admin-table-card__cta {
            white-space: nowrap;
        }

        .admin-table-filters {
            display: flex;
            align-items: center;
            gap: 12px;
            flex-wrap: wrap;
            margin-bottom: 20px;
        }

        .admin-table-filters input[type="text"],
        .admin-table-filters input[type="search"] {
            min-width: 220px;
            flex: 1 1 220px;
            padding: 12px 16px;
            border-radius: 12px;
            border: 1px solid #E2E8F0;
        }

        .admin-table-filters .nice-select,
        .admin-table-filters select,
        .admin-table-filters button {
            min-width: 160px;
        }

        .admin-table-alerts {
            margin-bottom: 20px;
        }

        .admin-table-alert {
            padding: 12px 16px;
            border-radius: 8px;
            margin-bottom: 12px;
            font-weight: 500;
        }

        .admin-table-alert--success {
            color: #1b5e20;
            background: #e8f5e9;
        }

        .admin-table-alert--error {
            color: #b42318;
            background: #fee4e2;
        }

        .admin-table {
            width: 100%;
            border-collapse: collapse;
        }

        .admin-table thead th,
        .admin-table tbody td {
            white-space: normal;
            vertical-align: middle;
            padding: 14px 18px;
        }

        .admin-table thead th {
            color: #475569;
            font-weight: 600;
            font-size: 14px;
            border-bottom: 1px solid #e2e8f0;
        }

        .admin-table tbody tr {
            border-bottom: 1px solid #f1f5f9;
        }

        .admin-table tbody tr:last-child {
            border-bottom: none;
        }

        .admin-table__empty td {
            text-align: center;
            padding: 32px;
            color: #64748b;
        }

        .admin-table__entry {
            display: flex;
            align-items: center;
            gap: 16px;
        }

        .admin-table__thumb {
            width: 56px;
            height: 56px;
            border-radius: 12px;
            overflow: hidden;
            background: #f1f5f9;
            flex-shrink: 0;
        }

        .admin-table__thumb img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .admin-table__entry-body {
            display: flex;
            flex-direction: column;
            gap: 4px;
        }

        .admin-table__entry-sub {
            color: #64748b;
            font-size: 14px;
        }

        .admin-table__pricing {
            display: flex;
            flex-direction: column;
            gap: 6px;
        }

        .admin-table__pricing del {
            color: #94a3b8;
            font-size: 14px;
        }

        .admin-table__status-badge {
            display: inline-flex;
            align-items: center;
            gap: 6px;
            padding: 4px 10px;
            border-radius: 999px;
            font-size: 12px;
            font-weight: 600;
            text-transform: capitalize;
        }

        .admin-table__status-badge--active {
            background: #dcfce7;
            color: #166534;
        }

        .admin-table__status-badge--inactive {
            background: #fee2e2;
            color: #b91c1c;
        }

        .admin-table__status-badge--draft {
            background: #fef3c7;
            color: #92400e;
        }

        .admin-table__status-badge--out_of_stock {
            background: #ede9fe;
            color: #5b21b6;
        }

        .admin-table__cell--actions {
            text-align: right;
            white-space: nowrap;
        }

        .admin-table__action-btn {
            min-width: 96px;
            justify-content: center;
        }

        .admin-table__action-btn + .admin-table__action-btn {
            margin-left: 8px;
        }

        .admin-table__action-btn--ghost {
            background: transparent;
            color: #2563eb;
            border-color: #bfdbfe;
        }

        .admin-table__action-btn--ghost:hover,
        .admin-table__action-btn--ghost:focus {
            background: #2563eb;
            color: #ffffff;
            border-color: #2563eb;
        }

        .admin-table__action-btn--danger {
            background: transparent;
            color: #b91c1c;
            border-color: #fecaca;
        }

        .admin-table__action-btn--danger:hover,
        .admin-table__action-btn--danger:focus {
            background: #dc2626;
            color: #ffffff;
            border-color: #dc2626;
        }

        .admin-table-pagination {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 20px;
            flex-wrap: wrap;
            gap: 12px;
            color: #475569;
            font-size: 14px;
        }

        .admin-table-pagination__controls {
            display: flex;
            gap: 8px;
        }

        @media (max-width: 991px) {
            .admin-table-filters {
                flex-direction: column;
                align-items: stretch;
            }

            .admin-table-filters input[type="text"],
            .admin-table-filters input[type="search"],
            .admin-table-filters .nice-select,
            .admin-table-filters select,
            .admin-table-filters button {
                width: 100%;
            }
        }

        @media (max-width: 767px) {
            .admin-table thead {
                display: none;
            }

            .admin-table tbody tr {
                display: block;
                border: 1px solid #edf2f7;
                border-radius: 12px;
                padding: 16px;
                margin-bottom: 16px;
            }

            .admin-table tbody tr td {
                display: flex;
                flex-direction: column;
                gap: 6px;
                padding: 8px 0;
                border: none;
            }

            .admin-table tbody tr td::before {
                content: attr(data-label);
                font-weight: 600;
                color: #64748b;
            }

            .admin-table__action-btn,
            .admin-table__action-btn + .admin-table__action-btn {
                width: 100%;
                margin-left: 0;
            }
        }
    </style>
</head>
<body>
    <div class="rcf_dashboard">
        <?php render_admin_sidebar($dashboardData['sidebar']); ?>
        <div class="right-area-body-content">
            <?php render_admin_header($dashboardData['header']); ?>
            <div class="body-root-inner">
                <div class="transection">
                    <div class="row g-5 align-items-start">
                        <div class="col-12">
                            <div class="admin-table-card mb--20">
                                <div class="admin-table-card__header">
                                    <div class="admin-table-card__heading">
                                        <h3 class="admin-table-card__title">Products</h3>
                                        <div class="admin-table-card__meta">
                                            <span class="admin-table-card__meta-item">Total products: <?= number_format($totalCount); ?></span>
                                            <?php if ($search !== ''): ?>
                                                <span class="admin-table-card__meta-item">Search: “<?= htmlspecialchars($search, ENT_QUOTES, 'UTF-8'); ?>”</span>
                                            <?php endif; ?>
                                            <?php if ($categoryFilter !== ''): ?>
                                                <span class="admin-table-card__meta-item">Category: <?= htmlspecialchars($categoryLookup[$categoryFilter] ?? 'Unknown', ENT_QUOTES, 'UTF-8'); ?></span>
                                            <?php endif; ?>
                                            <?php if ($statusFilter !== ''): ?>
                                                <span class="admin-table-card__meta-item">Status: <?= htmlspecialchars(product_status_label($statusFilter), ENT_QUOTES, 'UTF-8'); ?></span>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                    <a href="add-product.php" class="rts-btn btn-primary admin-table-card__cta">+ Add Product</a>
                                </div>

                                <form method="get" class="admin-table-filters">
                                    <input type="search" name="search" value="<?= htmlspecialchars($search, ENT_QUOTES, 'UTF-8'); ?>" placeholder="Search products" aria-label="Search products">
                                    <select name="category" class="mySelect" aria-label="Filter by category">
                                        <option value="" data-display="All Categories">All Categories</option>
                                        <?php foreach ($categoryOptions as $option): ?>
                                            <option value="<?= htmlspecialchars($option['id'], ENT_QUOTES, 'UTF-8'); ?>" data-display="<?= htmlspecialchars($option['name'], ENT_QUOTES, 'UTF-8'); ?>"<?= $categoryFilter === $option['id'] ? ' selected' : ''; ?>><?= htmlspecialchars($option['name'], ENT_QUOTES, 'UTF-8'); ?></option>
                                        <?php endforeach; ?>
                                    </select>
                                    <select name="status" class="mySelect" aria-label="Filter by status">
                                        <option value="" data-display="All Statuses">All Statuses</option>
                                        <?php foreach ($statusOptions as $value => $label): ?>
                                            <option value="<?= htmlspecialchars($value, ENT_QUOTES, 'UTF-8'); ?>" data-display="<?= htmlspecialchars($label, ENT_QUOTES, 'UTF-8'); ?>"<?= $statusFilter === $value ? ' selected' : ''; ?>><?= htmlspecialchars($label, ENT_QUOTES, 'UTF-8'); ?></option>
                                        <?php endforeach; ?>
                                    </select>
                                    <button type="submit" class="rts-btn btn-primary">Filter</button>
                                </form>

                                <?php if (!empty($flashMessages)): ?>
                                    <div class="admin-table-alerts" role="status" aria-live="polite">
                                        <?php foreach ($flashMessages as $type => $messages): ?>
                                            <?php foreach ($messages as $message): ?>
                                                <div class="admin-table-alert admin-table-alert--<?= $type === 'success' ? 'success' : 'error'; ?>">
                                                    <?= htmlspecialchars($message, ENT_QUOTES, 'UTF-8'); ?>
                                                </div>
                                            <?php endforeach; ?>
                                        <?php endforeach; ?>
                                    </div>
                                <?php endif; ?>

                                <div class="table-responsive">
                                    <table class="table table-hover admin-table">
                                        <thead>
                                            <tr>
                                                <th scope="col" style="min-width: 220px;">Product</th>
                                                <th scope="col" style="min-width: 140px;">Pricing</th>
                                                <th scope="col" style="min-width: 120px;">Status</th>
                                                <th scope="col" style="min-width: 120px;">Stock</th>
                                                <th scope="col" style="min-width: 140px;">Created</th>
                                                <th scope="col" style="min-width: 120px;" class="text-end">Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php if (empty($products)): ?>
                                                <tr class="admin-table__empty">
                                                    <td colspan="6">No products found. Try adjusting your filters or add a new product.</td>
                                                </tr>
                                            <?php else: ?>
                                                <?php foreach ($products as $product):
                                                    $productImage = resolve_product_image_url($product['image_path'] ?? null) ?? $placeholderProductImage;
                                                    $status = (string) ($product['status'] ?? 'inactive');
                                                    $statusLabel = product_status_label($status);
                                                    $statusModifier = product_status_modifier($status);
                                                    $stockQuantity = (int) ($product['stock_quantity'] ?? 0);
                                                    $sku = $product['sku'] ?? '';
                                                    $price = $product['price'] ?? null;
                                                    $salePrice = $product['sale_price'] ?? null;
                                                    $categoryName = $product['category_id'] && isset($categoryLookup[$product['category_id']]) ? $categoryLookup[$product['category_id']] : '—';
                                                ?>
                                                    <tr>
                                                        <td data-label="Product">
                                                            <div class="admin-table__entry">
                                                                <div class="admin-table__thumb">
                                                                    <img src="<?= htmlspecialchars($productImage, ENT_QUOTES, 'UTF-8'); ?>" alt="<?= htmlspecialchars($product['name'] ?? 'Product image', ENT_QUOTES, 'UTF-8'); ?>">
                                                                </div>
                                                                <div class="admin-table__entry-body">
                                                                    <strong><?= htmlspecialchars($product['name'] ?? 'Untitled product', ENT_QUOTES, 'UTF-8'); ?></strong>
                                                                    <?php if ($sku !== ''): ?>
                                                                        <span class="admin-table__entry-sub">SKU: <?= htmlspecialchars($sku, ENT_QUOTES, 'UTF-8'); ?></span>
                                                                    <?php endif; ?>
                                                                    <span class="admin-table__entry-sub">Category: <?= htmlspecialchars($categoryName, ENT_QUOTES, 'UTF-8'); ?></span>
                                                                </div>
                                                            </div>
                                                        </td>
                                                        <td data-label="Pricing">
                                                            <div class="admin-table__pricing">
                                                                <span><?= htmlspecialchars(format_product_price($price), ENT_QUOTES, 'UTF-8'); ?></span>
                                                                <?php if ($salePrice !== null && $salePrice !== '' && is_numeric($salePrice) && (float) $salePrice > 0): ?>
                                                                    <del><?= htmlspecialchars(format_product_price($salePrice), ENT_QUOTES, 'UTF-8'); ?></del>
                                                                <?php endif; ?>
                                                            </div>
                                                        </td>
                                                        <td data-label="Status">
                                                            <span class="admin-table__status-badge admin-table__status-badge--<?= htmlspecialchars($statusModifier, ENT_QUOTES, 'UTF-8'); ?>">
                                                                <?= htmlspecialchars($statusLabel, ENT_QUOTES, 'UTF-8'); ?>
                                                            </span>
                                                        </td>
                                                        <td data-label="Stock">
                                                            <div class="admin-table__entry-body">
                                                                <strong><?= number_format($stockQuantity); ?></strong>
                                                                <span class="admin-table__entry-sub"><?= $stockQuantity > 0 ? 'In inventory' : 'Reorder needed'; ?></span>
                                                            </div>
                                                        </td>
                                                        <td data-label="Created">
                                                            <?= htmlspecialchars(format_product_date($product['created_at'] ?? null), ENT_QUOTES, 'UTF-8'); ?>
                                                        </td>
                                                        <td data-label="Actions" class="admin-table__cell admin-table__cell--actions">
                                                            <a class="rts-btn btn-primary admin-table__action-btn admin-table__action-btn--ghost" href="add-product.php?action=edit&id=<?= rawurlencode($product['id']); ?>">Edit</a>
                                                            <button type="button" class="rts-btn btn-primary admin-table__action-btn admin-table__action-btn--danger product-delete-button admin-table__delete-button" data-product-id="<?= htmlspecialchars($product['id'], ENT_QUOTES, 'UTF-8'); ?>" data-product-name="<?= htmlspecialchars($product['name'] ?? 'this product', ENT_QUOTES, 'UTF-8'); ?>" aria-label="Delete product <?= htmlspecialchars($product['name'] ?? 'this product', ENT_QUOTES, 'UTF-8'); ?>">Delete</button>
                                                        </td>
                                                    </tr>
                                                <?php endforeach; ?>
                                            <?php endif; ?>
                                        </tbody>
                                    </table>
                                </div>

                                <?php if ($totalPages > 1): ?>
                                    <div class="admin-table-pagination">
                                        <div class="admin-table-pagination__info">
                                            Page <?= $currentPage; ?> of <?= $totalPages; ?>
                                        </div>
                                        <div class="admin-table-pagination__controls">
                                            <?php
                                                $prevPage = max(1, $currentPage - 1);
                                                $nextPage = min($totalPages, $currentPage + 1);
                                                $prevQuery = build_product_query(['page' => $prevPage]);
                                                $nextQuery = build_product_query(['page' => $nextPage]);
                                                $prevHref = 'product-list.php' . ($prevQuery !== '' ? '?' . $prevQuery : '');
                                                $nextHref = 'product-list.php' . ($nextQuery !== '' ? '?' . $nextQuery : '');
                                            ?>
                                            <a class="rts-btn btn-primary admin-table__action-btn admin-table__action-btn--ghost" href="<?= htmlspecialchars($prevHref, ENT_QUOTES, 'UTF-8'); ?>"<?= $currentPage === 1 ? ' aria-disabled="true" style="pointer-events:none; opacity:0.5;"' : ''; ?>>Prev</a>
                                            <a class="rts-btn btn-primary admin-table__action-btn admin-table__action-btn--ghost" href="<?= htmlspecialchars($nextHref, ENT_QUOTES, 'UTF-8'); ?>"<?= $currentPage === $totalPages ? ' aria-disabled="true" style="pointer-events:none; opacity:0.5;"' : ''; ?>>Next</a>
                                        </div>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
                <form method="post" id="delete-product-form" style="display:none;">
                    <input type="hidden" name="action" value="delete">
                    <input type="hidden" name="id" id="delete-product-id" value="">
                </form>
                <?php render_admin_footer($dashboardData['footer']); ?>
            </div>
        </div>
    </div>
    <?php render_admin_overlays(); ?>
    <script src="assets/js/plugins.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/apexcharts@3.27.0/dist/apexcharts.min.js"></script>
    <script src="https://cdn.datatables.net/1.10.15/js/jquery.dataTables.min.js"></script>
    <script src="assets/js/main.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function () {
            var deleteButtons = document.querySelectorAll('.product-delete-button');
            var deleteForm = document.getElementById('delete-product-form');
            var deleteIdField = document.getElementById('delete-product-id');

            function submitDeletion(productId, productName) {
                if (!deleteForm || !deleteIdField) {
                    return;
                }

                var message = 'Are you sure you want to delete the product "' + productName + '"? This action cannot be undone.';
                if (!window.confirm(message)) {
                    return;
                }

                deleteIdField.value = productId;
                deleteForm.submit();
            }

            deleteButtons.forEach(function (button) {
                button.addEventListener('click', function (event) {
                    event.preventDefault();
                    var productId = button.getAttribute('data-product-id');
                    var productName = button.getAttribute('data-product-name') || 'this product';
                    if (!productId) {
                        return;
                    }
                    submitDeletion(productId, productName);
                });
            });
        });
    </script>
</body>
</html>
