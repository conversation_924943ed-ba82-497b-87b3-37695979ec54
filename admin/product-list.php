<?php
$rootDir = null;

if (file_exists(__DIR__ . '/../bootstrap.php')) {
    $rootDir = __DIR__ . '/..';
} elseif (file_exists(dirname(__DIR__) . '/bootstrap.php')) {
    $rootDir = dirname(__DIR__);
} elseif (file_exists(realpath(__DIR__ . '/../bootstrap.php'))) {
    $rootDir = realpath(__DIR__ . '/..');
} elseif (file_exists('./bootstrap.php')) {
    $rootDir = '.';
} else {
    http_response_code(500);
    die('Error: Cannot locate bootstrap.php. Please check file structure.');
}

require_once $rootDir . '/bootstrap.php';
require_once $rootDir . '/components/admin/index.php';
require_once $rootDir . '/components/admin/categories/index.php';
require_once $rootDir . '/components/admin/products/index.php';

$productRepository = getProductRepository();
$productService = new ProductService($productRepository);
$statusOptions = $productService->getStatusOptions();

$categoryRepository = new CategoryRepository();
$categoryService = new CategoryService($categoryRepository);
$categoryOptions = $categoryService->listAllCategories();
$categoryLookup = [];
foreach ($categoryOptions as $categoryOption) {
    $categoryLookup[$categoryOption['id']] = $categoryOption['name'];
}

$requestMethod = $_SERVER['REQUEST_METHOD'] ?? 'GET';

$performProductDeletion = static function (string $deleteId) use ($productService) {
    if ($deleteId === '') {
        Flash::add('error', 'Product selection is invalid.');
        header('Location: product-list.php');
        exit;
    }

    $result = $productService->deleteProduct($deleteId);
    if ($result['success']) {
        Flash::add('success', $result['message'] ?? 'Product deleted successfully.');
    } else {
        $message = $result['message'] ?? ($result['errors']['general'] ?? 'Failed to delete product.');
        Flash::add('error', $message);
    }

    header('Location: product-list.php');
    exit;
};

if ($requestMethod === 'POST' && ($_POST['action'] ?? '') === 'delete') {
    $deleteId = isset($_POST['id']) ? trim((string) $_POST['id']) : '';
    $performProductDeletion($deleteId);
}

if (isset($_GET['action']) && $_GET['action'] === 'delete' && isset($_GET['id'])) {
    $deleteId = trim((string) $_GET['id']);
    $performProductDeletion($deleteId);
}

$page = isset($_GET['page']) ? max(1, (int) $_GET['page']) : 1;
$perPage = 10;
$search = trim((string) ($_GET['search'] ?? ''));
$statusFilter = $_GET['status'] ?? '';
if ($statusFilter !== '' && !array_key_exists($statusFilter, $statusOptions)) {
    $statusFilter = '';
}

$categoryFilter = trim((string) ($_GET['category'] ?? ''));
if ($categoryFilter !== '' && !array_key_exists($categoryFilter, $categoryLookup)) {
    $categoryFilter = '';
}

$pagination = $productService->paginateProducts($page, $perPage, $search ?: null, $statusFilter ?: null, $categoryFilter ?: null);
$products = $pagination['data'];
$totalPages = $pagination['total_pages'];
$currentPage = $pagination['current_page'];
$totalCount = $pagination['total'];

$flashMessages = Flash::consume();

$dashboardData = get_admin_dashboard_data();
foreach ($dashboardData['sidebar']['menu'] as &$menuItem) {
    if (($menuItem['href'] ?? '') === 'product-list.php') {
        $menuItem['is_active'] = true;
    }
}
unset($menuItem);

if (!function_exists('format_product_date')) {
    function format_product_date($timestamp): string
    {
        if ($timestamp instanceof DateTimeInterface) {
            $date = DateTimeImmutable::createFromInterface($timestamp);
            return $date->setTimezone(new DateTimeZone(date_default_timezone_get()))->format('M d, Y');
        }

        if (is_array($timestamp) && isset($timestamp['date'])) {
            $timestamp = $timestamp['date'];
        }

        if (!is_string($timestamp) || trim($timestamp) === '') {
            return '—';
        }

        try {
            $date = new DateTimeImmutable($timestamp, new DateTimeZone('UTC'));
            return $date->setTimezone(new DateTimeZone(date_default_timezone_get()))->format('M d, Y');
        } catch (Throwable $exception) {
            return $timestamp;
        }
    }
}

if (!function_exists('format_product_price')) {
    function format_product_price($price): string
    {
        if ($price === null || $price === '') {
            return '—';
        }

        if (!is_numeric($price)) {
            return (string) $price;
        }

        return '$' . number_format((float) $price, 2);
    }
}

if (!function_exists('product_status_label')) {
    function product_status_label(string $status): string
    {
        $labels = [
            'active' => 'Active',
            'inactive' => 'Inactive',
            'draft' => 'Draft',
            'out_of_stock' => 'Out of Stock',
        ];

        return $labels[$status] ?? ucfirst($status);
    }
}

if (!function_exists('product_status_modifier')) {
    function product_status_modifier(string $status): string
    {
        $allowed = ['active', 'inactive', 'draft', 'out_of_stock'];
        return in_array($status, $allowed, true) ? $status : 'inactive';
    }
}

if (!function_exists('build_product_query')) {
    function build_product_query(array $overrides = []): string
    {
        $base = [
            'search' => $_GET['search'] ?? null,
            'status' => $_GET['status'] ?? null,
            'category' => $_GET['category'] ?? null,
        ];
        $query = array_merge($base, $overrides);

        return http_build_query(array_filter($query, static fn ($value) => $value !== null && $value !== ''));
    }
}

$placeholderProductImage = 'assets/images/grocery/16.png';
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="description" content="rcf-Grocery-Store(e-Commerce) HTML Template: A sleek, responsive, and user-friendly HTML template designed for online grocery stores.">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="keywords" content="Grocery, Store, stores">
    <title>RC Furnishing Admin – Products</title>
    <link rel="shortcut icon" type="image/x-icon" href="assets/images/fav.png">
    <link rel="stylesheet preload" href="assets/css/plugins.css" as="style">
    <link rel="stylesheet preload" href="https://cdn.datatables.net/1.10.15/css/jquery.dataTables.min.css" as="style">
    <link rel="stylesheet preload" href="assets/css/style.css" as="style">
    <style>
        .product-page .product-card {
            padding: 24px;
            background: #ffffff;
            border-radius: 16px;
            box-shadow: 0 12px 30px rgba(35, 46, 60, 0.08);
        }

        .product-page__header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            gap: 16px;
            flex-wrap: wrap;
        }

        .product-page__filters {
            display: flex;
            align-items: center;
            gap: 12px;
            flex-wrap: wrap;
        }

        .product-page__filters input[type="text"] {
            min-width: 220px;
            flex: 1 1 220px;
            padding: 12px 16px;
            border-radius: 12px;
            border: 1px solid #E2E8F0;
        }

        .product-page__filters .nice-select,
        .product-page__filters select {
            min-width: 160px;
        }

        .product-page__meta {
            display: flex;
            flex-wrap: wrap;
            gap: 12px;
            align-items: center;
            color: #475569;
            font-size: 14px;
        }

        .product-page__alerts {
            margin-bottom: 20px;
        }

        .product-page__alert {
            padding: 12px 16px;
            border-radius: 8px;
            margin-bottom: 12px;
            font-weight: 500;
        }

        .product-page__alert--success {
            color: #1b5e20;
            background: #e8f5e9;
        }

        .product-page__alert--error {
            color: #d32f2f;
            background: #fdecea;
        }

        .product-page__table thead th,
        .product-page__table tbody td {
            white-space: normal;
            vertical-align: middle;
        }

        .product-page__product-cell {
            display: flex;
            align-items: center;
            gap: 16px;
        }

        .product-page__thumb {
            width: 56px;
            height: 56px;
            border-radius: 12px;
            overflow: hidden;
            background: #f1f5f9;
            flex-shrink: 0;
        }

        .product-page__thumb img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .product-page__product-meta {
            display: flex;
            flex-direction: column;
            gap: 4px;
        }

        .product-page__product-meta small {
            color: #64748b;
        }

        .product-page__pricing {
            display: flex;
            flex-direction: column;
            gap: 6px;
        }

        .product-page__pricing del {
            color: #94a3b8;
            font-size: 14px;
        }

        .product-page__status {
            display: inline-flex;
            align-items: center;
            gap: 6px;
            padding: 4px 10px;
            border-radius: 999px;
            font-size: 12px;
            font-weight: 600;
            text-transform: capitalize;
        }

        .product-page__status--active {
            background: #dcfce7;
            color: #166534;
        }

        .product-page__status--inactive {
            background: #fee2e2;
            color: #b91c1c;
        }

        .product-page__status--draft {
            background: #fef3c7;
            color: #92400e;
        }

        .product-page__status--out_of_stock {
            background: #ede9fe;
            color: #5b21b6;
        }

        .product-page__stock {
            display: flex;
            flex-direction: column;
            gap: 4px;
        }

        .product-page__stock span {
            font-size: 12px;
            color: #94a3b8;
        }

        .product-page__empty td {
            text-align: center;
        }

        .product-page__table .rts-btn.btn-danger {
            color: #d32f2f;
            border-color: #d32f2f;
            margin-left: 8px;
        }

        .product-page__table .rts-btn.btn-danger:hover {
            background-color: #d32f2f;
            color: #ffffff;
        }

        @media (max-width: 991px) {
            .product-page__filters {
                flex-direction: column;
                align-items: stretch;
            }

            .product-page__filters input[type="text"],
            .product-page__filters .nice-select,
            .product-page__filters select,
            .product-page__filters button {
                width: 100%;
            }
        }

        @media (max-width: 767px) {
            .product-page__table thead {
                display: none;
            }

            .product-page__table tbody tr {
                display: block;
                border: 1px solid #edf2f7;
                border-radius: 12px;
                padding: 16px;
                margin-bottom: 16px;
            }

            .product-page__table tbody tr td {
                display: flex;
                flex-direction: column;
                gap: 6px;
                padding: 8px 0;
                border: none;
            }

            .product-page__table tbody tr td::before {
                content: attr(data-label);
                font-weight: 600;
                color: #64748b;
            }

            .product-page__table tbody tr td:last-child .rts-btn {
                width: 100%;
                text-align: center;
            }
        }
    </style>
</head>
<body>
    <div class="rcf_dashboard">
        <?php render_admin_sidebar($dashboardData['sidebar']); ?>
        <div class="right-area-body-content">
            <?php render_admin_header($dashboardData['header']); ?>
            <div class="body-root-inner">
                <div class="transection product-page">
                    <div class="row g-5 align-items-start">
                        <div class="col-12">
                            <div class="product-card mb--20">
                                <div class="product-page__header mb--20">
                                    <div>
                                        <h3 class="title">Products</h3>
                                        <div class="product-page__meta">
                                            <span>Total products: <?= number_format($totalCount); ?></span>
                                            <?php if ($search !== ''): ?>
                                                <span>Search: "<?= htmlspecialchars($search, ENT_QUOTES, 'UTF-8'); ?>"</span>
                                            <?php endif; ?>
                                            <?php if ($categoryFilter !== ''): ?>
                                                <span>Category: <?= htmlspecialchars($categoryLookup[$categoryFilter] ?? 'Unknown', ENT_QUOTES, 'UTF-8'); ?></span>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                    <a href="add-product.php" class="rts-btn btn-primary">+ Add Product</a>
                                </div>

                                <form method="get" class="product-page__filters mb--20">
                                    <input type="text" name="search" value="<?= htmlspecialchars($search, ENT_QUOTES, 'UTF-8'); ?>" placeholder="Search products">
                                    <select name="category" class="mySelect">
                                        <option value="" data-display="All Categories">All Categories</option>
                                        <?php foreach ($categoryOptions as $option): ?>
                                            <option value="<?= htmlspecialchars($option['id'], ENT_QUOTES, 'UTF-8'); ?>" data-display="<?= htmlspecialchars($option['name'], ENT_QUOTES, 'UTF-8'); ?>"<?= $categoryFilter === $option['id'] ? ' selected' : ''; ?>><?= htmlspecialchars($option['name'], ENT_QUOTES, 'UTF-8'); ?></option>
                                        <?php endforeach; ?>
                                    </select>
                                    <select name="status" class="mySelect">
                                        <option value="" data-display="All Statuses">All Statuses</option>
                                        <?php foreach ($statusOptions as $value => $label): ?>
                                            <option value="<?= htmlspecialchars($value, ENT_QUOTES, 'UTF-8'); ?>" data-display="<?= htmlspecialchars($label, ENT_QUOTES, 'UTF-8'); ?>"<?= $statusFilter === $value ? ' selected' : ''; ?>><?= htmlspecialchars($label, ENT_QUOTES, 'UTF-8'); ?></option>
                                        <?php endforeach; ?>
                                    </select>
                                    <button type="submit" class="rts-btn btn-primary">Filter</button>
                                </form>

                                <?php if (!empty($flashMessages)): ?>
                                    <div class="product-page__alerts">
                                        <?php foreach ($flashMessages as $type => $messages): ?>
                                            <?php foreach ($messages as $message): ?>
                                                <div class="product-page__alert product-page__alert--<?= $type === 'success' ? 'success' : 'error'; ?>">
                                                    <?= htmlspecialchars($message, ENT_QUOTES, 'UTF-8'); ?>
                                                </div>
                                            <?php endforeach; ?>
                                        <?php endforeach; ?>
                                    </div>
                                <?php endif; ?>

                                <div class="table-responsive">
                                    <table class="table table-hover product-page__table">
                                        <thead>
                                            <tr>
                                                <th style="min-width: 220px;">Product</th>
                                                <th style="min-width: 140px;">Pricing</th>
                                                <th style="min-width: 140px;">Status</th>
                                                <th style="min-width: 120px;">Stock</th>
                                                <th style="min-width: 140px;">Created</th>
                                                <th style="min-width: 120px;" class="text-end">Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php if (empty($products)): ?>
                                                <tr class="product-page__empty">
                                                    <td colspan="6">No products found. Try adjusting your filters or add a new product.</td>
                                                </tr>
                                            <?php else: ?>
                                                <?php foreach ($products as $product):
                                                    $productImage = resolve_product_image_url($product['image_path'] ?? null) ?? $placeholderProductImage;
                                                    $status = (string) ($product['status'] ?? 'inactive');
                                                    $statusLabel = product_status_label($status);
                                                    $statusModifier = product_status_modifier($status);
                                                    $stockQuantity = (int) ($product['stock_quantity'] ?? 0);
                                                    $sku = $product['sku'] ?? '';
                                                    $price = $product['price'] ?? null;
                                                    $salePrice = $product['sale_price'] ?? null;
                                                    $categoryId = $product['category_id'] ?? '';
                                                    $categoryName = $categoryId !== '' && isset($categoryLookup[$categoryId]) ? $categoryLookup[$categoryId] : '—';
                                                ?>
                                                    <tr>
                                                        <td data-label="Product">
                                                            <div class="product-page__product-cell">
                                                                <div class="product-page__thumb">
                                                                    <img src="<?= htmlspecialchars($productImage, ENT_QUOTES, 'UTF-8'); ?>" alt="<?= htmlspecialchars($product['name'] ?? 'Product image', ENT_QUOTES, 'UTF-8'); ?>">
                                                                </div>
                                                                <div class="product-page__product-meta">
                                                                    <strong><?= htmlspecialchars($product['name'] ?? 'Untitled product', ENT_QUOTES, 'UTF-8'); ?></strong>
                                                                    <?php if ($sku !== ''): ?>
                                                                        <small>SKU: <?= htmlspecialchars($sku, ENT_QUOTES, 'UTF-8'); ?></small>
                                                                    <?php endif; ?>
                                                                    <small>Category: <?= htmlspecialchars($categoryName, ENT_QUOTES, 'UTF-8'); ?></small>
                                                                </div>
                                                            </div>
                                                        </td>
                                                        <td data-label="Pricing">
                                                            <div class="product-page__pricing">
                                                                <span><?= htmlspecialchars(format_product_price($price), ENT_QUOTES, 'UTF-8'); ?></span>
                                                                <?php if ($salePrice !== null && $salePrice !== '' && is_numeric($salePrice) && (float) $salePrice > 0): ?>
                                                                    <del><?= htmlspecialchars(format_product_price($salePrice), ENT_QUOTES, 'UTF-8'); ?></del>
                                                                <?php endif; ?>
                                                            </div>
                                                        </td>
                                                        <td data-label="Status">
                                                            <span class="product-page__status product-page__status--<?= htmlspecialchars($statusModifier, ENT_QUOTES, 'UTF-8'); ?>">
                                                                <?= htmlspecialchars($statusLabel, ENT_QUOTES, 'UTF-8'); ?>
                                                            </span>
                                                        </td>
                                                        <td data-label="Stock">
                                                            <div class="product-page__stock">
                                                                <strong><?= number_format($stockQuantity); ?></strong>
                                                                <span><?= $stockQuantity > 0 ? 'In inventory' : 'Reorder needed'; ?></span>
                                                            </div>
                                                        </td>
                                                        <td data-label="Created">
                                                            <?= htmlspecialchars(format_product_date($product['created_at'] ?? null), ENT_QUOTES, 'UTF-8'); ?>
                                                        </td>
                                                        <td data-label="Actions" class="text-end">
                                                            <a class="rts-btn btn-primary bg-transparent" href="add-product.php?action=edit&id=<?= rawurlencode($product['id']); ?>">Edit</a>
                                                            <button type="button" class="rts-btn btn-danger bg-transparent product-delete-button" data-product-id="<?= htmlspecialchars($product['id'], ENT_QUOTES, 'UTF-8'); ?>" data-product-name="<?= htmlspecialchars($product['name'] ?? 'this product', ENT_QUOTES, 'UTF-8'); ?>">Delete</button>
                                                        </td>
                                                    </tr>
                                                <?php endforeach; ?>
                                            <?php endif; ?>
                                        </tbody>
                                    </table>
                                </div>

                                <?php if ($totalPages > 1): ?>
                                    <div class="pagination" style="display:flex; justify-content:space-between; align-items:center; margin-top:20px;">
                                        <div>
                                            Page <?= $currentPage; ?> of <?= $totalPages; ?>
                                        </div>
                                        <div style="display:flex; gap:8px;">
                                            <?php
                                                $prevPage = max(1, $currentPage - 1);
                                                $nextPage = min($totalPages, $currentPage + 1);
                                                $prevQuery = build_product_query(['page' => $prevPage]);
                                                $nextQuery = build_product_query(['page' => $nextPage]);
                                                $prevHref = 'product-list.php' . ($prevQuery !== '' ? '?' . $prevQuery : '');
                                                $nextHref = 'product-list.php' . ($nextQuery !== '' ? '?' . $nextQuery : '');
                                            ?>
                                            <a class="rts-btn btn-primary bg-transparent" href="<?= htmlspecialchars($prevHref, ENT_QUOTES, 'UTF-8'); ?>"<?= $currentPage === 1 ? ' style="pointer-events:none; opacity:0.5;"' : ''; ?>>Prev</a>
                                            <a class="rts-btn btn-primary bg-transparent" href="<?= htmlspecialchars($nextHref, ENT_QUOTES, 'UTF-8'); ?>"<?= $currentPage === $totalPages ? ' style="pointer-events:none; opacity:0.5;"' : ''; ?>>Next</a>
                                        </div>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
                <form method="post" id="delete-product-form" style="display:none;">
                    <input type="hidden" name="action" value="delete">
                    <input type="hidden" name="id" id="delete-product-id" value="">
                </form>
                <?php render_admin_footer($dashboardData['footer']); ?>
            </div>
        </div>
    </div>
    <?php render_admin_overlays(); ?>
    <script src="assets/js/plugins.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/apexcharts@3.27.0/dist/apexcharts.min.js"></script>
    <script src="https://cdn.datatables.net/1.10.15/js/jquery.dataTables.min.js"></script>
    <script src="assets/js/main.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function () {
            var deleteButtons = document.querySelectorAll('.product-delete-button');
            var deleteForm = document.getElementById('delete-product-form');
            var deleteIdField = document.getElementById('delete-product-id');

            function submitDeletion(productId, productName) {
                if (!deleteForm || !deleteIdField) {
                    return;
                }

                var message = 'Are you sure you want to delete the product "' + productName + '"? This action cannot be undone.';
                if (!window.confirm(message)) {
                    return;
                }

                deleteIdField.value = productId;
                deleteForm.submit();
            }

            deleteButtons.forEach(function (button) {
                button.addEventListener('click', function (event) {
                    event.preventDefault();
                    var productId = button.getAttribute('data-product-id');
                    var productName = button.getAttribute('data-product-name') || 'this product';
                    if (!productId) {
                        return;
                    }
                    submitDeletion(productId, productName);
                });
            });
        });
    </script>
</body>
</html>
