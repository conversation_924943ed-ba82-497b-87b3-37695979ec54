<?php
require_once __DIR__ . '/../bootstrap.php';
require_once __DIR__ . '/../components/admin/index.php';
require_once __DIR__ . '/../components/admin/categories/index.php';

$repository = new CategoryRepository();
$service = new CategoryService($repository);
$statusOptions = $service->getStatusOptions();

$formMode = 'create';
$editingId = null;
$errors = [];
$formData = [
    'name' => '',
    'description' => '',
    'status' => 'active',
    'image_path' => '',
    'slug' => '',
];

$requestMethod = $_SERVER['REQUEST_METHOD'] ?? 'GET';
$action = $_GET['action'] ?? null;

if ($requestMethod === 'POST') {
    $formMode = ($_POST['form_action'] ?? 'create') === 'update' ? 'edit' : 'create';
    $editingId = isset($_POST['category_id']) ? trim((string) $_POST['category_id']) : null;
    if ($editingId === '') {
        $editingId = null;
    }
    $formData = [
        'name' => $_POST['name'] ?? '',
        'description' => $_POST['description'] ?? '',
        'status' => $_POST['status'] ?? 'active',
        'image_path' => $_POST['existing_image_path'] ?? '',
        'slug' => $_POST['slug'] ?? '',
    ];

    if ($formMode === 'edit' && !$editingId) {
        Flash::add('error', 'Category selection is invalid.');
        header('Location: add-category.php');
        exit;
    }

    $imageUpload = $_FILES['image'] ?? null;

    if ($formMode === 'create') {
        $result = $service->createCategory($formData, $imageUpload);
        if ($result['success']) {
            Flash::add('success', $result['message'] ?? 'Category created successfully.');
            header('Location: categories.php');
            exit;
        }
        $errors = $result['errors'];
        if (!empty($result['errors']['general'])) {
            Flash::add('error', $result['errors']['general']);
        }
    } else {
        $result = $service->updateCategory($editingId, $formData, $imageUpload);
        if ($result['success']) {
            Flash::add('success', $result['message'] ?? 'Category updated successfully.');
            header('Location: categories.php');
            exit;
        }
        $errors = $result['errors'];
        if (!empty($result['errors']['general'])) {
            Flash::add('error', $result['errors']['general']);
        }
    }
} elseif ($action === 'edit') {
    $editingId = isset($_GET['id']) ? trim((string) $_GET['id']) : null;
    if ($editingId === '') {
        $editingId = null;
    }
    if (!$editingId) {
        Flash::add('error', 'Please choose a category to edit.');
        header('Location: categories.php');
        exit;
    }
    $category = $service->getCategory($editingId);
    if ($category === null) {
        Flash::add('error', 'The requested category could not be found.');
        header('Location: categories.php');
        exit;
    }
    $formMode = 'edit';
    $formData = [
        'name' => $category['name'],
        'description' => $category['description'] ?? '',
        'status' => $category['status'],
        'image_path' => $category['image_path'] ?? '',
        'slug' => $category['slug'] ?? '',
    ];
}

if ($formMode === 'edit' && $editingId && $formData['image_path'] === '') {
    $existingCategory = $service->getCategory($editingId);
    if ($existingCategory !== null) {
        $formData['image_path'] = $existingCategory['image_path'] ?? '';
        if ($formData['slug'] === '') {
            $formData['slug'] = $existingCategory['slug'] ?? '';
        }
    }
}

$flashMessages = Flash::consume();

// Debug information for troubleshooting (only when debug=1 is in URL)
$debugInfo = null;
if (isset($_GET['debug']) && $_GET['debug'] === '1') {
    $debugInfo = [
        'mongodb_available' => is_mongodb_available(),
        'database_available' => is_database_available(),
        'total_categories' => 0,
        'connection_type' => 'unknown',
        'php_version' => PHP_VERSION,
        'session_status' => session_status(),
        'post_data_received' => !empty($_POST),
        'form_mode' => $formMode,
    ];

    try {
        $connection = getMongoConnection();
        $debugInfo['connection_type'] = $connection->isUsingMock() ? 'mock' : 'mongodb';
        $repo = getCategoryRepository();
        $debugInfo['total_categories'] = count($repo->find());
    } catch (Exception $e) {
        $debugInfo['connection_error'] = $e->getMessage();
    }
}

$dashboardData = get_admin_dashboard_data();
foreach ($dashboardData['sidebar']['menu'] as &$menuItem) {
    if (($menuItem['href'] ?? '') === 'categories.php') {
        $menuItem['is_active'] = true;
    }
}
unset($menuItem);

$pageTitle = $formMode === 'edit' ? 'Edit Category' : 'Add Category';
$formDescription = $formMode === 'edit'
    ? 'Update the category details below to keep your catalogue organised.'
    : 'Add a new category to organise your products effectively.';
$placeholderCategoryImage = 'assets/images/grocery/16.png';
$currentCategoryImage = resolve_category_image_url($formData['image_path'] ?? null) ?? $placeholderCategoryImage;
$imageUploadMessage = $formData['image_path'] !== ''
    ? 'Drag and drop or browse to replace the image.'
    : 'Drag and drop image or click browse to upload.';

if (!function_exists('generate_category_slug_preview')) {
    function generate_category_slug_preview(string $value): string
    {
        $value = strtolower(trim($value));
        $value = preg_replace('/[^a-z0-9]+/', '-', $value ?? '');
        $value = trim((string) $value, '-');
        return $value !== '' ? $value : 'new-category';
    }
}

$defaultPreviewName = 'New category';
$defaultPreviewDescription = 'Add a short description to help your team recognise this category.';
$initialSlugPreview = $formData['slug'] !== '' ? $formData['slug'] : generate_category_slug_preview($formData['name']);
$defaultSlugPreview = generate_category_slug_preview('');
$previewName = $formData['name'] !== '' ? $formData['name'] : $defaultPreviewName;
$previewDescription = $formData['description'] !== '' ? $formData['description'] : $defaultPreviewDescription;
$previewStatusLabel = $statusOptions[$formData['status']] ?? ucfirst($formData['status']);
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="description" content="rcf-Grocery-Store(e-Commerce) HTML Template: A sleek, responsive, and user-friendly HTML template designed for online grocery stores.">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="keywords" content="Grocery, Store, stores">
    <title>RC Furnishing Admin – <?= htmlspecialchars($pageTitle, ENT_QUOTES, 'UTF-8'); ?></title>
    <link rel="shortcut icon" type="image/x-icon" href="assets/images/fav.png">
    <link rel="stylesheet preload" href="assets/css/plugins.css" as="style">
    <link rel="stylesheet preload" href="https://cdn.datatables.net/1.10.15/css/jquery.dataTables.min.css" as="style">
    <link rel="stylesheet preload" href="assets/css/style.css" as="style">
    <style>
        .category-form-page .table-product-select {
            padding: 40px;
            background: #ffffff;
            border-radius: 24px;
            box-shadow: 0 24px 60px rgba(15, 23, 42, 0.08);
        }

        .category-form-page .right-collups-area-top {
            display: flex;
            align-items: flex-start;
            justify-content: space-between;
            gap: 24px;
            margin-bottom: 32px;
        }

        .category-form-page .right-collups-area-top .title {
            font-size: 34px;
            font-weight: 600;
            margin-bottom: 8px;
            color: #0f172a;
        }

        .category-form-page__subtitle {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            font-size: 13px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.08em;
            color: #6366f1;
        }

        .category-form-page .right-collups-area-top p {
            margin: 0;
            color: #64748b;
            max-width: 460px;
            line-height: 1.6;
        }

        .category-form-page__back {
            white-space: nowrap;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .category-form-page__alerts,
        .category-form-page__actions {
            margin-top: 0;
        }

        .category-form-page__alert {
            padding: 12px 16px;
            border-radius: 12px;
            font-weight: 500;
            margin-bottom: 12px;
        }

        .category-form-page__alert--success {
            color: #0f5132;
            background: #d1f2e1;
        }

        .category-form-page__alert--error {
            color: #b42318;
            background: #fee4e2;
        }

        .category-form {
            display: flex;
            flex-direction: column;
            gap: 28px;
        }

        .category-form-page__layout {
            display: grid;
            grid-template-columns: minmax(0, 1fr) minmax(280px, 320px);
            gap: 32px;
            align-items: flex-start;
        }

        .category-form-page__main {
            display: flex;
            flex-direction: column;
            gap: 28px;
        }

        .category-form-section {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 18px;
            padding: 28px 32px;
            display: flex;
            flex-direction: column;
            gap: 24px;
        }

        .category-form-section__header {
            display: flex;
            flex-direction: column;
            gap: 6px;
        }

        .category-form-section__kicker {
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.08em;
            color: #4f46e5;
        }

        .category-form-section__title {
            font-size: 22px;
            font-weight: 600;
            margin: 0;
            color: #0f172a;
        }

        .category-form-section__description {
            margin: 0;
            color: #64748b;
            max-width: 520px;
        }

        .category-form-section__grid {
            display: grid;
            gap: 20px;
            grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
        }

        .category-form-section__grid--single {
            grid-template-columns: 1fr;
        }

        .category-form-section__grid--two {
            grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
        }

        .category-form-page .single-input {
            display: flex;
            flex-direction: column;
            gap: 12px;
            margin: 0;
        }

        .category-form-page .single-input label {
            font-weight: 600;
            color: #0f172a;
        }

        .category-form-page .single-input input,
        .category-form-page .single-input textarea,
        .category-form-page .single-input select {
            width: 100%;
            border-radius: 12px;
        }

        .category-form-page textarea {
            min-height: 140px;
            resize: vertical;
        }

        .category-form-page .single-input input.error,
        .category-form-page .single-input textarea.error,
        .category-form-page .single-input select.error {
            border-color: #b42318;
            box-shadow: 0 0 0 2px rgba(180, 35, 24, 0.1);
        }

        .category-form-page .field-error {
            display: block;
            margin-top: -4px;
            font-size: 12px;
            color: #b42318;
        }

        .category-form-page .character-counter {
            display: block;
            margin-top: 4px;
            font-size: 12px;
            color: #94a3b8;
            text-align: right;
        }

        .category-form-page__helper {
            display: block;
            font-size: 12px;
            color: #94a3b8;
        }

        .category-form-slug {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 12px 16px;
            border-radius: 12px;
            border: 1px dashed #c7d2fe;
            background: #ffffff;
            font-weight: 500;
            color: #0f172a;
        }

        .category-form-slug__prefix {
            color: #64748b;
            font-size: 14px;
        }

        .category-form-slug__value {
            font-size: 15px;
            letter-spacing: 0.02em;
        }

        .category-image-upload__wrapper {
            display: grid;
            grid-template-columns: minmax(0, 1fr) minmax(0, 220px);
            gap: 24px;
            align-items: stretch;
        }

        .category-image-upload__dropzone {
            border: 1px dashed #d0d8e7;
            border-radius: 18px;
            background: #f8fafc;
            padding: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
            cursor: pointer;
        }

        .category-image-upload__dropzone:focus {
            outline: 2px solid #6366f1;
            outline-offset: 4px;
        }

        .category-image-upload__dropzone .profile-left {
            width: 100%;
        }

        .category-image-upload__dropzone .profile-image {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 12px;
            margin-bottom: 16px;
        }

        .category-image-upload__dropzone .profile-image img {
            width: 120px;
            height: 120px;
            border-radius: 18px;
            object-fit: cover;
            box-shadow: 0 12px 30px rgba(15, 23, 42, 0.12);
        }

        .category-image-upload__dropzone span {
            display: block;
            color: #64748b;
            font-size: 14px;
        }

        .category-image-upload__input input {
            display: none;
        }

        .category-image-upload__input {
            display: inline-flex;
            align-items: center;
            justify-content: center;
        }

        .category-image-upload__input .rts-btn {
            cursor: pointer;
        }

        .category-image-upload__current {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 12px;
            text-align: center;
            padding: 24px;
            border: 1px solid #e2e8f0;
            border-radius: 18px;
            background: #ffffff;
        }

        .category-image-upload__current img {
            width: 100%;
            border-radius: 16px;
            object-fit: cover;
            box-shadow: 0 16px 40px rgba(15, 23, 42, 0.12);
        }

        .category-image-upload__current-label {
            font-weight: 600;
            color: #0f172a;
        }

        .category-form-page__sidebar {
            display: flex;
            flex-direction: column;
            gap: 24px;
        }

        .category-preview-card {
            background: linear-gradient(135deg, rgba(79, 70, 229, 0.12), rgba(14, 116, 144, 0.08));
            border-radius: 20px;
            padding: 24px;
            display: flex;
            flex-direction: column;
            gap: 16px;
        }

        .category-preview-card__header {
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .category-preview-card__header h3 {
            margin: 0;
            font-size: 18px;
            color: #0f172a;
            font-weight: 600;
        }

        .category-preview-card__status {
            display: inline-flex;
            align-items: center;
            gap: 6px;
            padding: 4px 10px;
            border-radius: 999px;
            font-size: 12px;
            font-weight: 600;
            text-transform: capitalize;
        }

        .category-preview-card__status--active {
            background: #dcfce7;
            color: #166534;
        }

        .category-preview-card__status--inactive {
            background: #fee2e2;
            color: #b91c1c;
        }

        .category-preview-card__status--draft {
            background: #fef3c7;
            color: #92400e;
        }

        .category-preview-card__body {
            display: grid;
            gap: 16px;
        }

        .category-preview-card__media img {
            width: 100%;
            border-radius: 16px;
            object-fit: cover;
            box-shadow: 0 16px 32px rgba(15, 23, 42, 0.14);
        }

        .category-preview-card__content h4 {
            margin: 0;
            font-size: 18px;
            font-weight: 600;
            color: #0f172a;
        }

        .category-preview-card__content p {
            margin: 8px 0 0 0;
            color: #334155;
            line-height: 1.6;
        }

        .category-form-tips {
            background: #ffffff;
            border: 1px solid #e2e8f0;
            border-radius: 18px;
            padding: 20px 22px;
        }

        .category-form-tips h3 {
            margin: 0 0 12px 0;
            font-size: 16px;
            font-weight: 600;
            color: #0f172a;
        }

        .category-form-tips ul {
            margin: 0;
            padding-left: 18px;
            color: #475569;
            line-height: 1.6;
        }

        .category-form-callout {
            background: rgba(99, 102, 241, 0.08);
            border: 1px solid rgba(99, 102, 241, 0.2);
            border-radius: 16px;
            padding: 18px;
            color: #312e81;
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .category-form-callout strong {
            font-size: 14px;
        }

        .category-form-page__actions {
            display: flex;
            gap: 12px;
            flex-wrap: wrap;
        }

        .category-form-page__actions .rts-btn {
            min-width: 160px;
        }

        @media (max-width: 1200px) {
            .category-image-upload__wrapper {
                grid-template-columns: 1fr;
            }
        }

        @media (max-width: 991px) {
            .category-form-page .table-product-select {
                padding: 24px;
            }

            .category-form-page .right-collups-area-top {
                flex-direction: column;
                align-items: stretch;
            }

            .category-form-page__layout {
                grid-template-columns: 1fr;
            }

            .category-form-page__sidebar {
                order: -1;
            }
        }

        @media (max-width: 767px) {
            .category-form-section {
                padding: 20px 22px;
            }

            .category-form-page__actions {
                flex-direction: column;
            }

            .category-form-page__actions .rts-btn {
                width: 100%;
                text-align: center;
            }
        }
    </style>
</head>
<body>
    <div class="rcf_dashboard">
        <?php render_admin_sidebar($dashboardData['sidebar']); ?>
        <div class="right-area-body-content">
            <?php render_admin_header($dashboardData['header']); ?>
            <div class="body-root-inner">
                <div class="transection category-form-page">
                    <div class="vendor-list-main-wrapper product-wrapper add-product-page">
                        <div class="card-body table-product-select">
                            <div class="header-two show right-collups-add-product">
                                <div class="right-collups-area-top">
                                    <div>
                                        <div class="category-form-page__subtitle"><?= $formMode === 'edit' ? 'Update category' : 'Create category'; ?></div>
                                        <h1 class="title"><?= htmlspecialchars($pageTitle, ENT_QUOTES, 'UTF-8'); ?></h1>
                                        <p><?= htmlspecialchars($formDescription, ENT_QUOTES, 'UTF-8'); ?></p>
                                    </div>
                                    <a href="categories.php" class="rts-btn btn-primary bg-transparent category-form-page__back">
                                        <span aria-hidden="true">⟵</span>
                                        <span>Back to Categories</span>
                                    </a>
                                </div>
                                <?php if (!empty($flashMessages)): ?>
                                    <div class="category-form-page__alerts">
                                        <?php foreach ($flashMessages as $type => $messages): ?>
                                            <?php foreach ($messages as $message): ?>
                                                <div class="category-form-page__alert category-form-page__alert--<?= $type === 'success' ? 'success' : 'error'; ?>">
                                                    <?= htmlspecialchars($message, ENT_QUOTES, 'UTF-8'); ?>
                                                </div>
                                            <?php endforeach; ?>
                                        <?php endforeach; ?>
                                    </div>
                                <?php endif; ?>

                                <!-- Debug Information -->
                                <?php if ($debugInfo !== null): ?>
                                    <div class="category-form-page__alert category-form-page__alert--info" style="background: #e3f2fd; border-color: #2196f3; color: #1976d2;">
                                        <strong>Debug Information:</strong><br>
                                        MongoDB Available: <?= $debugInfo['mongodb_available'] ? 'Yes' : 'No'; ?><br>
                                        Database Available: <?= $debugInfo['database_available'] ? 'Yes' : 'No'; ?><br>
                                        Connection Type: <?= htmlspecialchars($debugInfo['connection_type'], ENT_QUOTES, 'UTF-8'); ?><br>
                                        Total Categories: <?= $debugInfo['total_categories']; ?><br>
                                        PHP Version: <?= htmlspecialchars($debugInfo['php_version'], ENT_QUOTES, 'UTF-8'); ?><br>
                                        Session Status: <?= $debugInfo['session_status'] === PHP_SESSION_ACTIVE ? 'Active' : 'Inactive'; ?><br>
                                        POST Data Received: <?= $debugInfo['post_data_received'] ? 'Yes' : 'No'; ?><br>
                                        Form Mode: <?= htmlspecialchars($debugInfo['form_mode'], ENT_QUOTES, 'UTF-8'); ?><br>
                                        <?php if (isset($debugInfo['connection_error'])): ?>
                                            Connection Error: <?= htmlspecialchars($debugInfo['connection_error'], ENT_QUOTES, 'UTF-8'); ?><br>
                                        <?php endif; ?>
                                        <small style="opacity: 0.8;">Debug mode is active. Remove ?debug=1 from URL for normal operation.</small>
                                    </div>
                                <?php endif; ?>

                                <form method="post" class="category-form" id="category-form" enctype="multipart/form-data">
                                    <input type="hidden" id="form-action" name="form_action" value="<?= $formMode === 'edit' ? 'update' : 'create'; ?>">
                                    <?php if ($formMode === 'edit' && $editingId): ?>
                                        <input type="hidden" id="category-id" name="category_id" value="<?= htmlspecialchars($editingId ?? '', ENT_QUOTES, 'UTF-8'); ?>">
                                    <?php endif; ?>
                                    <input type="hidden" id="existing-image-path" name="existing_image_path" value="<?= htmlspecialchars($formData['image_path'], ENT_QUOTES, 'UTF-8'); ?>">
                                    <?php if (!empty($errors['general'])): ?>
                                        <div class="category-form-page__alert category-form-page__alert--error">
                                            <?= htmlspecialchars($errors['general'], ENT_QUOTES, 'UTF-8'); ?>
                                        </div>
                                    <?php endif; ?>

                                    <div class="category-form-page__layout">
                                        <div class="category-form-page__main">
                                            <section class="category-form-section">
                                                <header class="category-form-section__header">
                                                    <span class="category-form-section__kicker">Details</span>
                                                    <h2 class="category-form-section__title">Category Details</h2>
                                                    <p class="category-form-section__description">Give the category a clear name and description so your team knows exactly where products belong.</p>
                                                </header>
                                                <div class="category-form-section__grid">
                                                    <div class="single-input">
                                                        <label for="category-name">Category Name <span style="color:#d32f2f;">*</span></label>
                                                        <input type="text" id="category-name" name="name" maxlength="<?= CategoryValidator::NAME_MAX_LENGTH; ?>" value="<?= htmlspecialchars($formData['name'], ENT_QUOTES, 'UTF-8'); ?>" placeholder="e.g. Seasonal Decor" autocomplete="off" required>
                                                        <?php if (!empty($errors['name'])): ?>
                                                            <small class="field-error">
                                                                <?= htmlspecialchars($errors['name'], ENT_QUOTES, 'UTF-8'); ?>
                                                            </small>
                                                        <?php endif; ?>
                                                    </div>
                                                    <div class="single-input">
                                                        <label for="category-slug">URL Slug</label>
                                                        <output class="category-form-slug" id="category-slug" name="slug-preview" aria-live="polite">
                                                            <span class="category-form-slug__prefix">/categories/</span>
                                                            <span class="category-form-slug__value" id="category-slug-preview" data-default-value="<?= htmlspecialchars($defaultSlugPreview, ENT_QUOTES, 'UTF-8'); ?>"><?= htmlspecialchars($initialSlugPreview, ENT_QUOTES, 'UTF-8'); ?></span>
                                                        </output>
                                                        <small class="category-form-page__helper">Slug updates automatically from the name. We&#39;ll adjust it on save if another category uses the same slug.</small>
                                                    </div>
                                                </div>
                                                <div class="single-input">
                                                    <label for="category-description">Description</label>
                                                    <textarea id="category-description" name="description" maxlength="<?= CategoryValidator::DESCRIPTION_MAX_LENGTH; ?>" placeholder="Describe the category to guide your merchandising team." autocomplete="off"><?= htmlspecialchars($formData['description'], ENT_QUOTES, 'UTF-8'); ?></textarea>
                                                    <?php if (!empty($errors['description'])): ?>
                                                        <small class="field-error">
                                                            <?= htmlspecialchars($errors['description'], ENT_QUOTES, 'UTF-8'); ?>
                                                        </small>
                                                    <?php endif; ?>
                                                </div>
                                            </section>

                                            <section class="category-form-section">
                                                <header class="category-form-section__header">
                                                    <span class="category-form-section__kicker">Visibility</span>
                                                    <h2 class="category-form-section__title">Publishing</h2>
                                                    <p class="category-form-section__description">Choose the status that reflects the category&rsquo;s readiness.</p>
                                                </header>
                                                <div class="category-form-section__grid category-form-section__grid--two">
                                                    <div class="single-input">
                                                        <label for="category-status">Status</label>
                                                        <select id="category-status" name="status" class="mySelect" autocomplete="off">
                                                            <?php foreach ($statusOptions as $value => $label): ?>
                                                                <option value="<?= htmlspecialchars($value, ENT_QUOTES, 'UTF-8'); ?>" data-display="<?= htmlspecialchars($label, ENT_QUOTES, 'UTF-8'); ?>"<?= $formData['status'] === $value ? ' selected' : ''; ?>><?= htmlspecialchars($label, ENT_QUOTES, 'UTF-8'); ?></option>
                                                            <?php endforeach; ?>
                                                        </select>
                                                        <?php if (!empty($errors['status'])): ?>
                                                            <small class="field-error">
                                                                <?= htmlspecialchars($errors['status'], ENT_QUOTES, 'UTF-8'); ?>
                                                            </small>
                                                        <?php endif; ?>
                                                    </div>
                                                    <div class="category-form-callout" role="note">
                                                        <strong>Need more time?</strong>
                                                        <span>Set the status to Draft while you finalise products. Switch to Active when you&rsquo;re ready to go live.</span>
                                                    </div>
                                                </div>
                                            </section>

                                            <section class="category-form-section">
                                                <header class="category-form-section__header">
                                                    <span class="category-form-section__kicker">Media</span>
                                                    <h2 class="category-form-section__title">Category Artwork</h2>
                                                    <p class="category-form-section__description">Upload an image that will represent the category across the storefront and admin dashboards.</p>
                                                </header>
                                                <div class="single-input category-image-upload">
                                                    <div class="category-image-upload__wrapper">
                                                        <div class="category-image-upload__dropzone" role="button" tabindex="0" aria-describedby="category-image-guidance">
                                                            <div class="profile-left">
                                                                <div class="profile-image">
                                                                    <img id="category-image-preview" src="<?= htmlspecialchars($currentCategoryImage, ENT_QUOTES, 'UTF-8'); ?>" data-original-src="<?= htmlspecialchars($currentCategoryImage, ENT_QUOTES, 'UTF-8'); ?>" alt="Category image preview">
                                                                    <span><?= htmlspecialchars($imageUploadMessage, ENT_QUOTES, 'UTF-8'); ?></span>
                                                                </div>
                                                                <div class="button-area">
                                                                    <label class="brows-file-wrapper category-image-upload__input">
                                                                        <input type="file" id="category-image" name="image" accept="image/png,image/jpeg,image/webp" autocomplete="off">
                                                                        <span class="rts-btn btn-primary">Browse Files</span>
                                                                    </label>
                                                                    <span class="category-form-page__helper" id="category-image-guidance">Upload a JPG, PNG, or WEBP image up to 2MB. Square images look best.</span>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <?php if (!empty($formData['image_path'])):
                                                            $resolvedExistingImage = resolve_category_image_url($formData['image_path']);
                                                            if ($resolvedExistingImage !== null): ?>
                                                            <div class="category-image-upload__current">
                                                                <span class="category-image-upload__current-label">Current image</span>
                                                                <img src="<?= htmlspecialchars($resolvedExistingImage, ENT_QUOTES, 'UTF-8'); ?>" alt="Current category image">
                                                                <span class="category-form-page__helper">Leave the field empty to keep this image.</span>
                                                            </div>
                                                        <?php endif; endif; ?>
                                                    </div>
                                                    <?php if (!empty($errors['image'])): ?>
                                                        <small class="field-error">
                                                            <?= htmlspecialchars($errors['image'], ENT_QUOTES, 'UTF-8'); ?>
                                                        </small>
                                                    <?php endif; ?>
                                                </div>
                                            </section>
                                        </div>
                                        <aside class="category-form-page__sidebar" id="category-sidebar">
                                            <div class="category-preview-card">
                                                <div class="category-preview-card__header">
                                                    <h3>Live Preview</h3>
                                                    <span class="category-preview-card__status category-preview-card__status--<?= htmlspecialchars($formData['status'], ENT_QUOTES, 'UTF-8'); ?>" id="category-preview-status" data-status="<?= htmlspecialchars($formData['status'], ENT_QUOTES, 'UTF-8'); ?>"><?= htmlspecialchars($previewStatusLabel, ENT_QUOTES, 'UTF-8'); ?></span>
                                                </div>
                                                <div class="category-preview-card__body">
                                                    <div class="category-preview-card__media">
                                                        <img id="category-preview-image" src="<?= htmlspecialchars($currentCategoryImage, ENT_QUOTES, 'UTF-8'); ?>" data-original-src="<?= htmlspecialchars($currentCategoryImage, ENT_QUOTES, 'UTF-8'); ?>" alt="Category artwork preview">
                                                    </div>
                                                    <div class="category-preview-card__content">
                                                        <h4 id="category-preview-name" data-default-value="<?= htmlspecialchars($defaultPreviewName, ENT_QUOTES, 'UTF-8'); ?>"><?= htmlspecialchars($previewName, ENT_QUOTES, 'UTF-8'); ?></h4>
                                                        <p id="category-preview-description" data-default-value="<?= htmlspecialchars($defaultPreviewDescription, ENT_QUOTES, 'UTF-8'); ?>"><?= htmlspecialchars($previewDescription, ENT_QUOTES, 'UTF-8'); ?></p>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="category-form-tips">
                                                <h3>Best Practices</h3>
                                                <ul>
                                                    <li>Keep names short and recognisable so buyers can scan quickly.</li>
                                                    <li>Use the description to guide other admins on when to use the category.</li>
                                                    <li>Upload crisp, square imagery (512×512) for the best storefront appearance.</li>
                                                </ul>
                                            </div>
                                        </aside>
                                    </div>

                                    <div class="category-form-page__actions">
                                        <button type="submit" class="rts-btn btn-primary" data-loading-text="Saving...">Save Category</button>
                                        <?php if ($formMode === 'edit'): ?>
                                            <a class="rts-btn btn-primary bg-transparent" href="categories.php">Cancel</a>
                                        <?php else: ?>
                                            <button type="reset" class="rts-btn btn-primary bg-transparent">Reset</button>
                                        <?php endif; ?>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
                <?php render_admin_footer($dashboardData['footer']); ?>
            </div>
        </div>
    </div>
    <?php render_admin_overlays(); ?>
    <script src="assets/js/plugins.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/apexcharts@3.27.0/dist/apexcharts.min.js"></script>
    <script src="https://cdn.datatables.net/1.10.15/js/jquery.dataTables.min.js"></script>
    <script src="assets/js/main.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function () {
            var form = document.getElementById('category-form');
            if (!form) {
                return;
            }

            var nameInput = form.querySelector('#category-name');
            var descriptionInput = form.querySelector('#category-description');
            var statusSelect = form.querySelector('#category-status');
            var submitButton = form.querySelector('button[type="submit"]');
            var imageInput = form.querySelector('#category-image');
            var imagePreview = document.getElementById('category-image-preview');
            var imageDropzone = form.querySelector('.category-image-upload__dropzone');
            var imageBrowse = form.querySelector('.category-image-upload__input');
            var slugPreview = document.getElementById('category-slug-preview');

            var previewName = document.getElementById('category-preview-name');
            var previewDescription = document.getElementById('category-preview-description');
            var previewStatus = document.getElementById('category-preview-status');
            var previewImage = document.getElementById('category-preview-image');

            var statusLabels = <?= json_encode($statusOptions, JSON_HEX_TAG | JSON_HEX_APOS | JSON_HEX_QUOT | JSON_HEX_AMP); ?>;

            var defaultSlug = slugPreview ? (slugPreview.dataset.defaultValue || slugPreview.textContent.trim()) : 'new-category';
            var defaultName = previewName ? (previewName.dataset.defaultValue || previewName.textContent.trim()) : 'New category';
            var defaultDescription = previewDescription ? (previewDescription.dataset.defaultValue || previewDescription.textContent.trim()) : 'Add a short description to help your team recognise this category.';
            var originalPreviewImage = previewImage ? (previewImage.getAttribute('data-original-src') || previewImage.src) : null;
            var originalFormImage = imagePreview ? (imagePreview.getAttribute('data-original-src') || imagePreview.src) : null;

            function openImagePicker() {
                if (imageInput) {
                    imageInput.click();
                }
            }

            function slugify(value) {
                return value
                    .toString()
                    .toLowerCase()
                    .trim()
                    .replace(/[^a-z0-9]+/g, '-')
                    .replace(/^-+|-+$/g, '') || defaultSlug;
            }

            function updateSlug(value) {
                if (!slugPreview) {
                    return;
                }

                var finalValue = value && value.trim() !== '' ? slugify(value) : defaultSlug;
                slugPreview.textContent = finalValue;
            }

            function updatePreviewName(value) {
                if (!previewName) {
                    return;
                }

                var displayValue = value && value.trim() !== '' ? value.trim() : defaultName;
                previewName.textContent = displayValue;
            }

            function updatePreviewDescription(value) {
                if (!previewDescription) {
                    return;
                }

                var displayValue = value && value.trim() !== '' ? value.trim() : defaultDescription;
                previewDescription.textContent = displayValue;
            }

            function updateStatusBadge(value) {
                if (!previewStatus) {
                    return;
                }

                var label = statusLabels[value] || value;
                previewStatus.textContent = label;
                previewStatus.dataset.status = value;

                previewStatus.classList.remove(
                    'category-preview-card__status--active',
                    'category-preview-card__status--inactive',
                    'category-preview-card__status--draft'
                );
                previewStatus.classList.add('category-preview-card__status--' + value);
            }

            if (imageDropzone) {
                imageDropzone.addEventListener('click', openImagePicker);
                imageDropzone.addEventListener('keydown', function (event) {
                    if (event.key === 'Enter' || event.key === ' ') {
                        event.preventDefault();
                        openImagePicker();
                    }
                });
            }

            if (imageBrowse) {
                imageBrowse.addEventListener('click', openImagePicker);
                imageBrowse.addEventListener('keydown', function (event) {
                    if (event.key === 'Enter' || event.key === ' ') {
                        event.preventDefault();
                        openImagePicker();
                    }
                });
            }

            if (imageInput) {
                imageInput.addEventListener('change', function (event) {
                    var file = event.target.files && event.target.files[0];
                    if (file) {
                        var objectUrl = URL.createObjectURL(file);
                        if (imagePreview) {
                            imagePreview.src = objectUrl;
                            imagePreview.onload = function () {
                                URL.revokeObjectURL(objectUrl);
                            };
                        }
                        if (previewImage) {
                            previewImage.src = objectUrl;
                        }
                    } else {
                        if (imagePreview && originalFormImage) {
                            imagePreview.src = originalFormImage;
                        }
                        if (previewImage && originalPreviewImage) {
                            previewImage.src = originalPreviewImage;
                        }
                    }
                });
            }

            if (nameInput) {
                addCharacterCounter(nameInput, <?= CategoryValidator::NAME_MAX_LENGTH; ?>);
                nameInput.addEventListener('blur', function () {
                    validateField(nameInput, <?= CategoryValidator::NAME_MAX_LENGTH; ?>, 'Category name');
                });
                nameInput.addEventListener('input', function () {
                    if (nameInput.value.trim() !== '') {
                        validateField(nameInput, <?= CategoryValidator::NAME_MAX_LENGTH; ?>, 'Category name');
                    }
                    updateSlug(nameInput.value);
                    updatePreviewName(nameInput.value);
                });

                updateSlug(nameInput.value);
                updatePreviewName(nameInput.value);
            }

            if (descriptionInput) {
                addCharacterCounter(descriptionInput, <?= CategoryValidator::DESCRIPTION_MAX_LENGTH; ?>);
                descriptionInput.addEventListener('blur', function () {
                    validateField(descriptionInput, <?= CategoryValidator::DESCRIPTION_MAX_LENGTH; ?>, 'Description');
                });
                descriptionInput.addEventListener('input', function () {
                    validateField(descriptionInput, <?= CategoryValidator::DESCRIPTION_MAX_LENGTH; ?>, 'Description');
                    updatePreviewDescription(descriptionInput.value);
                });

                updatePreviewDescription(descriptionInput.value);
            }

            if (statusSelect) {
                statusSelect.addEventListener('change', function () {
                    updateStatusBadge(statusSelect.value);
                });
                updateStatusBadge(statusSelect.value);
            }

            form.addEventListener('submit', function (e) {
                // Debug logging (remove in production)
                if (window.location.search.includes('debug=1')) {
                    console.log('Form submission started');
                    console.log('Form data:', new FormData(form));
                }

                var isFormValid = true;

                if (nameInput) {
                    isFormValid = validateField(nameInput, <?= CategoryValidator::NAME_MAX_LENGTH; ?>, 'Category name') && isFormValid;
                }
                if (descriptionInput) {
                    isFormValid = validateField(descriptionInput, <?= CategoryValidator::DESCRIPTION_MAX_LENGTH; ?>, 'Description') && isFormValid;
                }

                if (!isFormValid) {
                    if (window.location.search.includes('debug=1')) {
                        console.log('Form validation failed');
                    }
                    e.preventDefault();
                    return false;
                }

                if (window.location.search.includes('debug=1')) {
                    console.log('Form validation passed, submitting...');
                }

                if (submitButton && !submitButton.dataset.submitting) {
                    submitButton.dataset.submitting = 'true';
                    submitButton.classList.add('disabled');
                    submitButton.disabled = true;
                    var loadingText = submitButton.getAttribute('data-loading-text') || 'Saving...';
                    submitButton.dataset.originalLabel = submitButton.innerHTML;
                    submitButton.innerHTML = loadingText;
                }
            });

            form.addEventListener('reset', function () {
                window.setTimeout(function () {
                    if (nameInput) {
                        updateSlug(nameInput.value);
                        updatePreviewName(nameInput.value);
                    }
                    if (descriptionInput) {
                        updatePreviewDescription(descriptionInput.value);
                    }
                    if (statusSelect) {
                        updateStatusBadge(statusSelect.value);
                    }
                    if (imagePreview && originalFormImage) {
                        imagePreview.src = originalFormImage;
                    }
                    if (previewImage && originalPreviewImage) {
                        previewImage.src = originalPreviewImage;
                    }
                }, 0);
            });

            function validateField(field, maxLength, fieldName) {
                var value = field.value.trim();
                var errorElement = field.parentNode.querySelector('.field-error');

                if (errorElement) {
                    errorElement.remove();
                }
                field.classList.remove('error');

                var isValid = true;
                var errorMessage = '';

                if (field === nameInput && value === '') {
                    isValid = false;
                    errorMessage = 'Category name is required.';
                } else if (value.length > maxLength) {
                    isValid = false;
                    errorMessage = fieldName + ' must be ' + maxLength + ' characters or fewer.';
                }

                if (!isValid) {
                    var errorDiv = document.createElement('small');
                    errorDiv.className = 'field-error';
                    errorDiv.textContent = errorMessage;
                    field.parentNode.appendChild(errorDiv);
                    field.classList.add('error');
                } else {
                    field.classList.remove('error');
                }

                return isValid;
            }

            function addCharacterCounter(field, maxLength) {
                if (field.parentNode.querySelector('.character-counter')) {
                    return;
                }

                var counter = document.createElement('small');
                counter.className = 'character-counter';
                field.parentNode.appendChild(counter);

                function updateCounter() {
                    var remaining = maxLength - field.value.length;
                    counter.textContent = remaining + ' characters remaining';
                    counter.style.color = remaining < 20 ? '#d32f2f' : '#64748b';
                }

                updateCounter();
                field.addEventListener('input', updateCounter);
            }
        });
    </script>
</body>
</html>
