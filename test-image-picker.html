<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Image Picker Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .test-section {
            margin: 30px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        
        .test-section h2 {
            margin-top: 0;
            color: #333;
        }
        
        /* Category styles (original working implementation) */
        .category-image-upload__input input {
            display: none;
        }
        
        .category-image-upload__input {
            display: inline-flex;
            align-items: center;
            justify-content: center;
        }
        
        .category-image-upload__input .rts-btn {
            cursor: pointer;
        }
        
        /* Product styles (fixed implementation) */
        .product-image-upload__input input {
            display: none;
        }
        
        .product-image-upload__input {
            display: inline-flex;
            align-items: center;
            justify-content: center;
        }
        
        .product-image-upload__input .rts-btn {
            cursor: pointer;
        }
        
        .rts-btn {
            background: #6366f1;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
        }
        
        .rts-btn:hover {
            background: #5855eb;
        }
        
        .status {
            margin-top: 10px;
            padding: 10px;
            border-radius: 4px;
            font-weight: bold;
        }
        
        .success {
            background: #d1fae5;
            color: #065f46;
            border: 1px solid #a7f3d0;
        }
        
        .error {
            background: #fee2e2;
            color: #991b1b;
            border: 1px solid #fca5a5;
        }
    </style>
</head>
<body>
    <h1>Image Picker Functionality Test</h1>
    <p>This test verifies that both category and product image pickers work correctly with the same implementation pattern.</p>
    
    <div class="test-section">
        <h2>Category Image Picker (Original Working Implementation)</h2>
        <label class="brows-file-wrapper category-image-upload__input">
            <input type="file" id="category-image" name="image" accept="image/png,image/jpeg,image/webp" autocomplete="off">
            <span class="rts-btn btn-primary">Browse Files</span>
        </label>
        <div id="category-status" class="status" style="display: none;"></div>
    </div>
    
    <div class="test-section">
        <h2>Product Image Picker (Fixed Implementation)</h2>
        <label class="brows-file-wrapper product-image-upload__input">
            <input type="file" id="product-image" name="product_image" accept="image/png,image/jpeg,image/webp" autocomplete="off">
            <span class="rts-btn btn-primary">Browse Files</span>
        </label>
        <div id="product-status" class="status" style="display: none;"></div>
    </div>
    
    <script>
        document.addEventListener('DOMContentLoaded', function () {
            // Test category image picker
            var categoryInput = document.getElementById('category-image');
            var categoryStatus = document.getElementById('category-status');
            var categoryBrowse = document.querySelector('.category-image-upload__input');
            
            // Test product image picker
            var productInput = document.getElementById('product-image');
            var productStatus = document.getElementById('product-status');
            var productBrowse = document.querySelector('.product-image-upload__input');
            
            function showStatus(statusElement, message, isSuccess) {
                statusElement.textContent = message;
                statusElement.className = 'status ' + (isSuccess ? 'success' : 'error');
                statusElement.style.display = 'block';
            }
            
            // Category image picker event handlers
            if (categoryInput && categoryBrowse) {
                categoryInput.addEventListener('change', function() {
                    if (this.files.length > 0) {
                        showStatus(categoryStatus, `✓ Category image picker working! Selected: ${this.files[0].name}`, true);
                    }
                });
                
                categoryBrowse.addEventListener('click', function() {
                    categoryInput.click();
                });
                
                categoryBrowse.addEventListener('keydown', function(event) {
                    if (event.key === 'Enter' || event.key === ' ') {
                        event.preventDefault();
                        categoryInput.click();
                    }
                });
            }
            
            // Product image picker event handlers
            if (productInput && productBrowse) {
                productInput.addEventListener('change', function() {
                    if (this.files.length > 0) {
                        showStatus(productStatus, `✓ Product image picker working! Selected: ${this.files[0].name}`, true);
                    }
                });
                
                productBrowse.addEventListener('click', function() {
                    productInput.click();
                });
                
                productBrowse.addEventListener('keydown', function(event) {
                    if (event.key === 'Enter' || event.key === ' ') {
                        event.preventDefault();
                        productInput.click();
                    }
                });
            }
            
            // Test if elements are found
            setTimeout(function() {
                if (!categoryInput || !categoryBrowse) {
                    showStatus(categoryStatus, '✗ Category image picker elements not found', false);
                }
                if (!productInput || !productBrowse) {
                    showStatus(productStatus, '✗ Product image picker elements not found', false);
                }
            }, 100);
        });
    </script>
</body>
</html>
