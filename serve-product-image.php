<?php

$disk = isset($_GET['disk']) ? (string) $_GET['disk'] : '';
$file = isset($_GET['file']) ? (string) $_GET['file'] : '';

if ($disk === '' || $file === '') {
    http_response_code(404);
    exit;
}

// Validate disk parameter
if (preg_match('/^[a-z0-9_-]+$/i', $disk) !== 1) {
    http_response_code(404);
    exit;
}

// Validate filename format - allow various naming strategies
// Supports: timestamp, UUID, hash, and hybrid naming patterns
$validPatterns = [
    // Timestamp pattern: product-image-20240919-123456.jpg
    '/^[a-z0-9]+(?:-[a-z0-9]+)*-[0-9]{8}-[0-9]{6}(?:-[0-9]+)?\.(jpg|jpeg|png|gif|webp)$/i',
    // UUID pattern: product-image-550e8400-e29b-41d4-a716-************.jpg
    '/^[a-z0-9]+(?:-[a-z0-9]+)*-[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}(?:-[0-9]+)?\.(jpg|jpeg|png|gif|webp)$/i',
    // Hash pattern: product-image-a1b2c3d4e5f6g7h8.jpg
    '/^[a-z0-9]+(?:-[a-z0-9]+)*-[0-9a-f]{16}(?:-[0-9]+)?\.(jpg|jpeg|png|gif|webp)$/i',
    // Hybrid pattern: product-image-20240919-123456-abc123.jpg
    '/^[a-z0-9]+(?:-[a-z0-9]+)*-[0-9]{8}-[0-9]{6}-[0-9a-f]{6}(?:-[0-9]+)?\.(jpg|jpeg|png|gif|webp)$/i',
    // Fallback pattern: product-image-fallback-1726747123.456-abc12345-12345.jpg
    '/^[a-z0-9]+(?:-[a-z0-9]+)*-fallback-[0-9]+\.[0-9]+-[0-9a-f]+-[0-9]+\.(jpg|jpeg|png|gif|webp)$/i',
];

$isValidFilename = false;
foreach ($validPatterns as $pattern) {
    if (preg_match($pattern, $file) === 1) {
        $isValidFilename = true;
        break;
    }
}

if (!$isValidFilename) {
    http_response_code(404);
    exit;
}

$projectRoot = __DIR__;
$directories = [
    'assets' => $projectRoot . '/assets/uploads/products',
    'storage' => $projectRoot . '/storage/uploads/products',
    'temp' => rtrim(sys_get_temp_dir(), DIRECTORY_SEPARATOR) . '/rcf-product-images',
    'custom' => null, // Will be set dynamically if needed
];

if (!array_key_exists($disk, $directories)) {
    http_response_code(404);
    exit;
}

$directory = $directories[$disk];
if ($directory === null) {
    http_response_code(404);
    exit;
}

$filePath = rtrim($directory, DIRECTORY_SEPARATOR) . DIRECTORY_SEPARATOR . $file;

// Security check: ensure the resolved path is within the expected directory
$realDirectory = realpath($directory);
$realFilePath = realpath($filePath);

if ($realDirectory === false || $realFilePath === false || !str_starts_with($realFilePath, $realDirectory)) {
    http_response_code(404);
    exit;
}

if (!is_file($realFilePath) || !is_readable($realFilePath)) {
    http_response_code(404);
    exit;
}

// Get file info
$fileSize = filesize($realFilePath);
$mimeType = 'application/octet-stream';

// Detect MIME type
if (function_exists('mime_content_type')) {
    $detectedMime = mime_content_type($realFilePath);
    if ($detectedMime !== false) {
        $mimeType = $detectedMime;
    }
} elseif (class_exists('finfo')) {
    $finfo = new finfo(FILEINFO_MIME_TYPE);
    $detectedMime = $finfo->file($realFilePath);
    if ($detectedMime !== false) {
        $mimeType = $detectedMime;
    }
}

// Validate that it's actually an image
$allowedMimeTypes = [
    'image/jpeg',
    'image/jpg', 
    'image/png',
    'image/gif',
    'image/webp',
];

if (!in_array($mimeType, $allowedMimeTypes)) {
    http_response_code(404);
    exit;
}

// Set security headers
header('X-Content-Type-Options: nosniff');
header('X-Frame-Options: DENY');
header('X-XSS-Protection: 1; mode=block');

// Set caching headers for better performance
$etag = md5_file($realFilePath);
$lastModified = filemtime($realFilePath);

header('ETag: "' . $etag . '"');
header('Last-Modified: ' . gmdate('D, d M Y H:i:s', $lastModified) . ' GMT');
header('Cache-Control: public, max-age=31536000'); // 1 year cache

// Check if client has cached version
$clientEtag = $_SERVER['HTTP_IF_NONE_MATCH'] ?? '';
$clientLastModified = $_SERVER['HTTP_IF_MODIFIED_SINCE'] ?? '';

if (($clientEtag && $clientEtag === '"' . $etag . '"') || 
    ($clientLastModified && strtotime($clientLastModified) >= $lastModified)) {
    http_response_code(304);
    exit;
}

// Set content headers
header('Content-Type: ' . $mimeType);
header('Content-Length: ' . $fileSize);
header('Content-Disposition: inline; filename="' . basename($file) . '"');

// Handle range requests for large images
$range = $_SERVER['HTTP_RANGE'] ?? '';
if ($range && str_starts_with($range, 'bytes=')) {
    $ranges = explode(',', substr($range, 6));
    $range = explode('-', $ranges[0]);
    $start = intval($range[0]);
    $end = isset($range[1]) && $range[1] !== '' ? intval($range[1]) : $fileSize - 1;
    
    if ($start < $fileSize && $end < $fileSize && $start <= $end) {
        http_response_code(206);
        header('Accept-Ranges: bytes');
        header("Content-Range: bytes $start-$end/$fileSize");
        header('Content-Length: ' . ($end - $start + 1));
        
        $handle = fopen($realFilePath, 'rb');
        if ($handle !== false) {
            fseek($handle, $start);
            echo fread($handle, $end - $start + 1);
            fclose($handle);
        }
        exit;
    }
}

// Output the file
$handle = fopen($realFilePath, 'rb');
if ($handle !== false) {
    // Output in chunks for memory efficiency
    while (!feof($handle)) {
        echo fread($handle, 8192);
        if (connection_aborted()) {
            break;
        }
    }
    fclose($handle);
} else {
    http_response_code(500);
}

exit;
